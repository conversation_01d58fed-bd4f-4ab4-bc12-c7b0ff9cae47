<description>
Exemple:
### Tickets
<< 1..N (idTickets) TicketsHistorique
1..0 << 1..1 (idHistorique) not_sended_emails : idHistorique

Cet exemple se lit:
La table Ticket est référence par la table TicketsHistorique à travers la colonne idTickets dans une relation One To Many (1 Ticket, N TicketsHistorique), elle même référencé par la table not_sended_emails par la colonne idHistorique dans une relation One To One, sachant qu’il peut n’y avoir aucune entrée not_sended_emails pour un historique (1..0).
</description>
### ab_contrats
- F_COMPTET 0..N << 1..1 (ct_num) ab_contrats
- F_ABONNEMENT 0..N << 1..1 (ab_no) ab_contrats
- Pole  0..N << 1..1 (idPole) ab_contrats

### ab_contratsTemps
- ab_contrats 0..N << 1..N (ct_num) ab_contratsTemps
- Pole  0..N << 1..1 (idPole) ab_contratsTemps

### Commandes
Il existe des cas où il y a eu plusieurs commandes assigné à un seul tickets, mais l’id de ces tickets étant inférieur à 22000, je considère cela comme une anomalie et non la norme.
- Tickets 0..N << 1..1 (idTickets) Commandes
- F_COMPTET (CT_Num, CT_Intitule) 0..N << 1..1 (ct_num, Client) Commandes
- Pole (libelle) 0..N << 1..1 (pole)                                                          Commandes
- Utilisateurs (nom + ' ' + prenom) 0..N <<  0..1 (tech1, tech2)         Commandes
- CommandesPlanification 1..1 >> 0..N (idCommandes) Commandes

### CommandesPrestation
- Commandes 0..N << 1..1 (commande) CommandesPrestation
- Prestations 0..N << 1..1 (libelle) CommandesPrestation

### CommandesPlanification
- Commandes 0..N << 1..1 (idCOmmandes) CommandesPlanification
- Utilisateurs (nom + ' ' + prenom) 0..N <<  0..1 (tech1, tech2) CommandesPlanification

### Consultation
- Tickets 0..N << 1..1 (idTickets)                                                   Consultation
- Utilisateurs  (nom + ' ' + prenom) 0..N  <<  1..1  (utilisateur)   Consultation

### Contrats
- F_COMPTET 0..N << 1..1 (ct_num) Contrats

### ClientsVIP
- F_COMPTET 0..N << 1..1 (ct_num) ClientsVIP
- Pole 0..N << 1..1 (idPole) ClientsVIP

### Contacts
- ContactsLiaison (idContacts) 1..1 >> 0..N Contacts
- Tickets (ct_num) 1..1 >> 0..N Contacts
- Tickets (demandeur) 1..1 >> 0..N (nom + ' ' + prenom) Contacts
- InventaireBureautique (ct_num) 1..1 >> 0..N Contacts

### ContactsLiaison
- Contacts 0..N << 1..1 (idContacts) ContactsLiaison #Sert à relier un contact à un autre un ct_num que celui de la table contacts

### demande_bureautique
- Tickets 0..N << 1..1 (idTickets, client)                          demande_bureautique
- Utilisateurs (nom + ' ' + prenom) 0..N <<  1..1 (tech) demande_bureautique
- TicketsEquipement 0..N << 0..1 (sn) demande_bureautiqueerventions
- Tickets 0..N << 1..1 (idTickets)                                                                      Interventions
- Utilisateurs (nom + ' ' + prenom) 0..N <<  0..1 (technicien, technicien2) Interventions

### InventaireBureautique
- Tickets 0..N << 1..1 (idTickets)                                                   inventaireBureautique
- Utilisateurs  (nom + ' ' + prenom) 0..N  <<  1..1  (utilisateur)   inventaireBureautique
- Contacts 0..N << 1..1 (ct_num)                                                   inventaireBureautique
#NB: Potentielle FK sur ‘sn’ (numero de serie copieur)

### Installations
- Tickets  0..1 << 1..1 (idTickets) Installations
- Commande 0..1 << 1..1 (commande) Installations
- TypeInstall (libelle) 0..N << 1..1 (type) Installations
- Utilisateurs (nom + ' ' + prenom) 0..N >>  0..1 (technicien, technicien2) Installations

### Interventions
- Tickets 0..N << 1..1 (idTickets) Interventions
- Utilisateurs (nom + ' ' + prenom) 0..N << 0..1 (technicien, technicien2) Interventions

### LogContrat
- F_COMPTET 0..N << 1..1 (ct_num) LogContrat

### meetings
- Tickets (idTickets) 0..N << 0..1 (ticket_id)                                  meetings
- Utilisateurs (idUtilisateurs) 0..N << 1..1 (tech_id, tech2_id)      meetings

```csharp
public class MeetingData
{
    public string Id { get; set; }
    public string Titre { get; set; }
    public string Contenu { get; set; }
    public string Description { get; set; }
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public string Client { get; set; }
    public int Duree { get; set; }
    public string Adresse { get; set; }
    public string Email1 { get; set; }
    public string Email2 { get; set; }
    public int Rappel { get; set; } = 15;
    public int Parent_Id { get; set; }
    public int TicketId { get; set; }
    public string MasterO365 { get; set; }
    public int IdIntervention { get; set; }
    public int IdPlanification { get; set; }
    public string IdO365 { get; set; }
    public string MeetingType { get; set; }
    public MeetingRecurrenceData RecurrenceData { get; set; }
    public MeetingData MasterMeetingData { get; set; }
    public User Tech1 { get; set; } 
    public User Tech2 { get; set; }
}

```

### meeting_childs
- Tickets (idTickets) 0..N << 0..1 (ticket_id)                                  meeting_childs
- Utilisateurs (idUtilisateurs) 0..N << 1..1 (tech_id, tech2_id)      meeting_childs
- meetings(id) 0..N << 1..1 (parent_id)                                          meeting_childs 

### Notifications
- Tickets (idTickets) 0..N << 1..1 (id_tickets) notifications
- TicketsHistorique (idHistorique) 0..N << 1..1 (id_historique) notifications

### not_sended_emails
- Tickets 0..N << 1..1 (idTickets) not_sended_emails
- TicketsHistorique 0..N << 1..1 (idHistorique) not_sended_emails

### Prets
- Tickets 0..N << 1..1 (idTickets) Prets
- Utilisateurs  (nom + ' ' + prenom) 0..N  <<  1..1  (utilisateur)   Prets
- PretsHistorique 1..1 >> 0..N (idPrets) Prets
#NB: Potentielle FK sur ‘Utilisateurs:utilisateur’, ‘equipement?'

### PretsHistorique
- Tickets           0..N  <<  1..1  (idTickets)                                        PretsHistorique
- Prets                0..N  <<  1..1  (idPrets)                                           PretsHistorique
- Utilisateurs  (nom + ' ' + prenom) 0..N  <<  1..1  (utilisateur)   PretsHistorique

### Prestations
- Pole 0..N << 1..1 (idPole) Prestations
- CommandesPrestation 1..1 >> 0..N (libelle) Prestations

### Qualiopi
- Tickets 0..1 << 1..1 (idTickets) Qualiopi

### Raccourcis
- F_COMPTET 0..N << 1..1 (ct_num) Raccourcis

### Reponse
- Pole 0..N << 1..1 (idPole) Reponse

### report_tracking
- Tickets 0..N << 1..1 (idTickets) report_tracking

### Sorties
- Tickets 0..N << 1..1 (idTickets, ct_num) Sorties
- F_ARTICLE (ar_ref) 0..N << 1..1 (reference) Sorties
- TicketsEquipement 0..N << 0..1 (sn) Sorties
- tech_depots (DE_NO) 0..N << 1..1 (depot_source) Sorties

### Specificites
- F_COMPTET 0..N << 1..1 (ct_num) Specificites

### SuiteInstallation
- Tickets 0..1 << 1..1 (idTickets) SuiteInstallation
- Tickets (idTickets) 0..N << 1..1 (idTicketsInstallation) SuiteInstallation
- Installation 0..N << 1..1 (idTicketsInstallation) Suite Installation
#NB: Lie 2 Ticket (idTickets: ticket type ‘Suite Installation’, idTicketsInstallation: ticket d’origine type 'Installation')

### tech_depots
- Sorties (depot_source) 1..1 >> 0..N (DE_No) tech_depots
- users 0..N << 1..1 (username) tech_depots

### Tickets
Listes des tables référençant l’idTickets (FK) : (dépend de >> table)
- 0..N << 1..1 (idTickets) TicketsHistorique
- 0..N << 1..1 (idTickets) notifications
- 0..N << 1..1 (idTickets) not_sended_emails
- 0..1 << 1..1 (idTickets) Qualiopi
- 0..1 << 1..1 (idTickets) Installations
- 0..1 << 1..1 (idTickets) SuiteInstallation
- (idTickets) 0..1 << 1..1 (idTicketsInstallation) SuiteInstallation
- 0..1 << 1..1 (idTickets) ticket_rapport
- 0..N << 1..1 (idTickets) TicketsContrats
- 0..N << 1..1 (idTickets) Prets
- 0..N  <<  1..1  (idTickets) PretsHistorique
- (idTickets) 0..N << 0..1 (ticket_id) meetings
- (idTickets) 0..N << 0..1 (ticket_id) meeting_childs
- 0..N << 1..1 (idTickets) inventaireBureautique
- 0..1 << 1..1 (idTickets) TicketsEquipement
- 0..N << 1..1 (idTickets) TicketsEtapes
- 0..N << 1..1 (idTickets)    TicketsDestinataires
- 0..N << 1..1 (idTickets, ct_num) Sorties
- 0..N << 1..1 (idTickets) Intervention
- 0..N << 1..1 (idTickets, client) demande_bureautique
- 0..N << 1..1 (idTickets) Commandes
- 0..N << 1..1 (idTickets) Consultation
- 0..N << 1..1 (idTickets) report_tracking

Tables que référence Tickets: (dépend de >> (col) table : col_ticket)
- (client) 1..1 >> 0..N (CT_Intitule) F_COMPTET
- (ct_num) 1..1 >> 0..N Contacts
- (type) 1..1 >> 0..N (libelle) Type
- (status) 1..1 >> 0..N (libelle) status
- (pole) 1..1 >> 0..N (libelle) Pole
- (demandeur) 1..1 >> 0..N (nom + ' ' + prenom) Contacts
- (assigne) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- (categorie, categorie2, categorie3) 0..1 >> 0..N (libelle) Categorie

```csharp
public class Ticket
{
public int idTickets { get; set; } //PK
public string ct_num { get; set; } //FK :: Contacts
public string client { get; set; } //FK :: F_COMPTET
public DateTime dateCreation { get; set; }
public string demandeur { get; set; } //FK :: Utilisateurs
public string pole { get; set; } //FK :: Pole
public string type { get; set; } //FK :: type
public string status { get; set; } //FK :: status
public string priorite { get; set; }
public int niveau { get; set; }
public string categorie { get; set; }  //FK :: Categorie
public string categorie2 { get; set; } //FK :: Categorie
public string categorie3 { get; set; } //FK :: Categorie
public int tempsTotal { get; set; }
public string titre { get; set; }
public string description { get; set; }
public DateTime dateRappel { get; set; }
public DateTime dateResolution { get; set; }
public int statusTemps { get; set; }
public int avertissement { get; set; }
public string dernierCorrespondant { get; set; }
public DateTime datePremiereReponse { get; set; }
public DateTime dateDerniereReponse { get; set; }
public bool notificationEnabled { get; set; }
}
```

### TicketsHistorique
- Tickets 0..N << 1..1 (idTickets)                            TicketsHistorique
- not_sended_emails (idHistorique) 1..1 >> 0..N  TicketsHistorique
- notifications (id_historique) 1..1 >> 0..N (idHistorique) TicketsHistorique

```csharp
public class TicketHistorique //Correspond à chaque note d'un ticket
{
public int idHistorique { get; set; } //PK
public int idTickets { get; set; } //FK :: Tickets
public DateTime datModificationn { get; set; }
public string correspondant { get; set; }
public string description { get; set; }
public int noteInterne { get; set; }
public int pieceJointe { get; set; }
public int envoiEmail { get; set; }
public int noteType { get; set; }
public int temps { get;set; }
}
```

### TicketsEquipement
La limitation 1..1 pour la relation Tickets-TicketsEquipement est récente et n’a pas été forcée, il y a un risque non négligeable d’invalidité sur cette règle en cas de validation de la donnée, tel quel.
- Tickets 0..1 << 1..1 (idTickets) TicketsEquipement
#NB: Potentielle FK sur ‘sn’ (numéro de serie) pour une table equipement (actuellement sur DB ERP), libelle qui provient de la même tâble

### TicketsEtapes
- Tickets 0..N << 1..1 (idTickets) TicketsEtapes

### TicketsDestinataires
- Tickets 0..N << 1..1 (idTickets)    TicketsDestinataires
- Contacts 0..N << 1..1 idContacts TicketsDestinataires

### TypeInstall
- Installations (type) 1..1 >> 0..N (libelle) TypeInstall

### ticket_rapport
- Tickets 0..1 << 1..1 (idTickets) ticket_rapport

### TicketsContrats
- Tickets 0..N << 1..1 (idTickets) TicketsContrats
- F_ABOLIGNE (AL_Design) 0..N << 1..1 (libelle) TicketsContrats
- F_ARTICLE (AR_Ref) 0..N << 1..1 (reference) TicketsContrats

### Utilisateurs
- Tickets (assigne) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- CommandesPlanification(tech1, tech2) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- Commandes(tech1,tech2) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- Consultation(utilisateur) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- InventaireBureautique(tech) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- Installations(technicien,technicien2) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- Prets(utilisateur) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs
- PretsHistorique(utilisateur) 0..1 >> 0..N (nom + ' + prenom) Utilisateurs

### UtilisateursPole
- Utilisateurs 0..N << 1..1 (idUtilisateur) UtilisateursPole
- Pole 0..N << 1..1 (idUtilisateur) Pole