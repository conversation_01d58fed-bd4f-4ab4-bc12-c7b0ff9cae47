#!/bin/bash
# ETL CLI Command Runner for Unix/Linux/macOS
# Usage: ./etl-cli.sh <command> [options]

set -e

echo "ETL CLI - Starting..."

# Auto-detect JAVA_HOME if not set
if [ -z "$JAVA_HOME" ]; then
    echo "JAVA_HOME not set, attempting to detect Java installation..."

    # Try common Java locations
    for java_dir in \
        /usr/lib/jvm/java-* \
        /usr/lib/jvm/jdk-* \
        /usr/lib/jvm/openjdk-* \
        /opt/java/* \
        /Library/Java/JavaVirtualMachines/*/Contents/Home \
        /System/Library/Java/JavaVirtualMachines/*/Contents/Home; do

        if [ -d "$java_dir" ] && [ -x "$java_dir/bin/java" ]; then
            export JAVA_HOME="$java_dir"
            echo "Found Java at: $JAVA_HOME"
            break
        fi
    done

    # If still not found, try to use java from PATH
    if [ -z "$JAVA_HOME" ] && command -v java &> /dev/null; then
        echo "Using Java from PATH"
    elif [ -z "$JAVA_HOME" ]; then
        echo "Error: Java is not installed or not in PATH"
        echo "Please install Java 17 or higher and set JAVA_HOME"
        echo "Or ensure java is in your PATH"
        exit 1
    fi
fi

# Test Java installation
if [ -n "$JAVA_HOME" ]; then
    "$JAVA_HOME/bin/java" -version >/dev/null 2>&1 || {
        echo "Error: Java installation test failed"
        echo "Please check your Java installation"
        exit 1
    }
else
    java -version >/dev/null 2>&1 || {
        echo "Error: Java installation test failed"
        echo "Please check your Java installation"
        exit 1
    }
fi

echo "Java: OK"

# Set the JAR file path
JAR_FILE="target/etl-0.0.1-SNAPSHOT.jar"

# Check if JAR file exists
if [ ! -f "$JAR_FILE" ]; then
    echo "Error: JAR file not found at $JAR_FILE"
    echo ""
    echo "To build the project, run one of:"
    echo "  ./build.sh                      (automated build)"
    echo "  make build                      (if you have make)"
    echo "  ./mvnw clean package -DskipTests  (manual build)"
    echo ""
    exit 1
fi

echo "JAR file found: $JAR_FILE"

# Run the ETL CLI command
echo "Running ETL with arguments: $*"
if [ -n "$JAVA_HOME" ]; then
    "$JAVA_HOME/bin/java" -jar "$JAR_FILE" "$@"
else
    java -jar "$JAR_FILE" "$@"
fi

EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo "ETL execution failed with exit code: $EXIT_CODE"
fi

exit $EXIT_CODE
