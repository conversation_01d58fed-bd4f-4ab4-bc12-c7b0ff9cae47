# ETL Spring Boot Project Makefile
# This Makefile provides a unified build system for the ETL project

# Variables
PROJECT_NAME = etl
VERSION = 0.0.1-SNAPSHOT
JAR_FILE = target/$(PROJECT_NAME)-$(VERSION).jar
MAIN_CLASS = com.helios.etl.EtlApplication

# Default Java options
JAVA_OPTS = -Xmx2g -XX:+UseG1GC

# Detect OS
ifeq ($(OS),Windows_NT)
    MAVEN_CMD = mvnw.cmd
    JAVA_CMD = java
    RM = del /Q
    MKDIR = mkdir
    PATHSEP = ;
else
    MAVEN_CMD = ./mvnw
    JAVA_CMD = java
    RM = rm -f
    MKDIR = mkdir -p
    PATHSEP = :
endif

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help:
	@echo "ETL Project Build System"
	@echo "========================"
	@echo ""
	@echo "Available targets:"
	@echo "  build         - Clean and build the project"
	@echo "  package       - Package the application into a JAR"
	@echo "  test          - Run all tests"
	@echo "  clean         - Clean build artifacts"
	@echo "  run           - Run the ETL application"
	@echo "  cli           - Run the ETL CLI with arguments (use: make cli ARGS='--run')"
	@echo "  install       - Install dependencies"
	@echo "  check-deps    - Check if all dependencies are available"
	@echo "  fix-maven     - Fix Maven wrapper issues"
	@echo "  setup         - Complete setup (fix maven + build)"
	@echo ""
	@echo "Examples:"
	@echo "  make build"
	@echo "  make cli ARGS='--run'"
	@echo "  make cli ARGS='--help'"

# Check dependencies
.PHONY: check-deps
check-deps:
	@echo "Checking dependencies..."
	@$(JAVA_CMD) -version || (echo "Error: Java not found in PATH" && exit 1)
	@echo "Java: OK"
	@if [ -f "$(MAVEN_CMD)" ]; then \
		echo "Maven wrapper: OK"; \
	else \
		echo "Warning: Maven wrapper not found, will try to fix"; \
	fi

# Fix Maven wrapper
.PHONY: fix-maven
fix-maven:
	@echo "Fixing Maven wrapper..."
	@if [ ! -f "mvnw" ] || [ ! -f "mvnw.cmd" ]; then \
		echo "Downloading Maven wrapper..."; \
		curl -L https://github.com/takari/maven-wrapper/archive/maven-wrapper-0.5.6.tar.gz | tar -xz --strip-components=1 maven-wrapper-maven-wrapper-0.5.6/mvnw maven-wrapper-maven-wrapper-0.5.6/mvnw.cmd maven-wrapper-maven-wrapper-0.5.6/.mvn; \
	fi
	@chmod +x mvnw

# Clean target
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
ifeq ($(OS),Windows_NT)
	@if exist target rmdir /s /q target
else
	@rm -rf target/
endif
	@echo "Clean completed."

# Install dependencies
.PHONY: install
install: check-deps
	@echo "Installing dependencies..."
	@$(MAVEN_CMD) dependency:resolve

# Compile target
.PHONY: compile
compile: check-deps
	@echo "Compiling project..."
	@$(MAVEN_CMD) compile

# Test target
.PHONY: test
test: check-deps
	@echo "Running tests..."
	@$(MAVEN_CMD) test

# Package target
.PHONY: package
package: check-deps
	@echo "Packaging application..."
	@$(MAVEN_CMD) clean package -DskipTests
	@echo "JAR file created: $(JAR_FILE)"

# Build target (compile + package)
.PHONY: build
build: clean package
	@echo "Build completed successfully!"
	@echo "JAR file: $(JAR_FILE)"

# Run the application normally
.PHONY: run
run: package
	@echo "Running ETL application..."
	@$(JAVA_CMD) $(JAVA_OPTS) -jar $(JAR_FILE)

# Run CLI with arguments
.PHONY: cli
cli: package
	@echo "Running ETL CLI..."
ifeq ($(ARGS),)
	@$(JAVA_CMD) $(JAVA_OPTS) -jar $(JAR_FILE) --help
else
	@$(JAVA_CMD) $(JAVA_OPTS) -jar $(JAR_FILE) $(ARGS)
endif

# Complete setup
.PHONY: setup
setup: fix-maven build
	@echo "Setup completed!"
	@echo "You can now run:"
	@echo "  make cli ARGS='--run'    # Run ETL"
	@echo "  make cli ARGS='--help'   # Show help"

# Development targets
.PHONY: dev-run
dev-run: compile
	@echo "Running in development mode..."
	@$(MAVEN_CMD) spring-boot:run

.PHONY: dev-debug
dev-debug: compile
	@echo "Running in debug mode..."
	@$(MAVEN_CMD) spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

# Lint and format
.PHONY: lint
lint:
	@echo "Running code quality checks..."
	@$(MAVEN_CMD) checkstyle:check spotbugs:check

.PHONY: format
format:
	@echo "Formatting code..."
	@$(MAVEN_CMD) fmt:format

# Show project info
.PHONY: info
info:
	@echo "Project Information"
	@echo "==================="
	@echo "Name: $(PROJECT_NAME)"
	@echo "Version: $(VERSION)"
	@echo "JAR File: $(JAR_FILE)"
	@echo "Main Class: $(MAIN_CLASS)"
	@echo ""
	@$(MAVEN_CMD) --version 2>/dev/null || echo "Maven wrapper not available"
	@echo ""
	@$(JAVA_CMD) -version

# Clean everything including dependencies
.PHONY: clean-all
clean-all: clean
	@echo "Cleaning all artifacts including dependencies..."
	@$(MAVEN_CMD) dependency:purge-local-repository -DmanualInclude="com.helios:etl"
