# Projet ETL Spring

## Aperçu

Le projet ETL Spring est un outil de migration de données construit avec Spring Boot qui extrait les données d'une base de données SQL Server source (Helios V1), les transforme selon des règles métier et les charge dans une base de données MySQL cible (Helios V2). Le projet se concentre sur la migration des données du système de tickets, principalement les "Tickets" et leurs données historiques associées.

Cet outil fournit à la fois une interface en ligne de commande (CLI) pour l'exécution manuelle et peut être intégré dans des applications web existantes. Il propose un suivi de progression en temps réel, une gestion des erreurs et des capacités de reprise.

## Fonctionnalités Clés

- **Architecture Multi-Base de Données** : Fonctionne avec SQL Server (source), MySQL (cible) et SQLite (suivi de progression)
- **CLI Complet** : Interface en ligne de commande riche avec plusieurs commandes et reporting de progression en temps réel
- **Suivi de Progression** : Reporting de progression en temps réel avec suivi persistant utilisant SQLite
- **Gestion des Erreurs** : Gestion et reporting complets des erreurs tout au long du processus ETL
- **Capacité de Reprise** : Possibilité de reprendre les processus ETL depuis le dernier point réussi
- **Design Modulaire** : Architecture bien structurée avec une séparation claire des préoccupations

## Architecture

### Approche Trois Bases de Données

1. **Base de Données Source (SQL Server)** : Contient les données du système de tickets hérité
2. **Base de Données Cible (MySQL)** : Reçoit les données transformées dans un nouveau schéma
3. **Base de Données de Suivi de Progression (SQLite)** : Suit l'exécution et le statut des processus ETL

### Composants Principaux

- **Couche d'Accès aux Données** : Utilise Spring JDBC Template pour les données sources et Spring Data JPA pour les données cibles/de progression
- **Couche de Logique Métier** : Logique de transformation centrale qui convertit les entités sources en entités cibles
- **Interface CLI** : Processeur de commandes avec reporting de progression en temps réel
- **Système de Cache** : Cache de données centralisé qui charge toutes les données sources et cibles au démarrage

## Prérequis

- Java 17 ou supérieur
- Maven 3.6 ou supérieur (optionnel, car le projet inclut Maven Wrapper)
- Accès à la base de données SQL Server source
- Accès à la base de données MySQL cible

## Construction de l'Application

```bash
# Windows
mvnw.cmd clean package

# Unix/Linux/macOS
./mvnw clean package
```

Ou utilisez le script de construction fourni :

```bash
# Windows
build.bat

# Unix/Linux/macOS
./build.sh
```

## Exécution de l'Application

### Méthode 1 : Utilisation des Scripts d'Aide (Recommandé)

**Windows :**
```cmd
etl-cli.bat --command run-etl
```

**Unix/Linux/macOS :**
```bash
./etl-cli.sh --command run-etl
```

### Méthode 2 : Exécution Directe avec Java

```bash
java -jar target/etl-0.0.1-SNAPSHOT.jar --command run-etl
```

## Commandes Disponibles

| Commande | Description |
|----------|-------------|
| `run-etl` / `full-etl` | Exécuter le processus ETL complet |
| `status` | Afficher le statut actuel de l'ETL |
| `progress` | Afficher des informations détaillées sur la progression |
| `resume-info` | Afficher des informations sur les exécutions reprendables |
| `resume-etl` | Reprendre l'ETL depuis le dernier point réussi |
| `list-runs` | Lister les exécutions ETL récentes |
| `--help` / `-h` | Afficher les informations d'aide |

## Configuration

Les configurations de base de données sont gérées par les propriétés d'application de Spring Boot. Mettez à jour les fichiers suivants avec vos identifiants de base de données :

- `src/main/resources/application.properties` (configuration principale)
- `src/main/resources/application-source.properties` (base de données source)
- `src/main/resources/application-target.properties` (base de données cible)
- `src/main/resources/application-progress.properties` (suivi de progression)

## Structure du Projet

```
src/
├── main/
│   ├── java/com/helios/etl/
│   │   ├── batch/           # Implémentation Spring Batch
│   │   ├── cli/             # Composants de l'interface en ligne de commande
│   │   ├── config/          # Configuration de la base de données et de l'application
│   │   ├── helper/          # Classes utilitaires
│   │   ├── model/           # Modèles de données
│   │   ├── outer/           # Composants d'intégration externe
│   │   ├── progress/        # Entités et services de suivi de progression
│   │   ├── repository/      # Dépôts d'accès aux données
│   │   ├── services/        # Services de logique métier
│   │   ├── source/          # Entités et dépôts de la base de données source
│   │   └── utils/           # Classes utilitaires
│   └── resources/           # Fichiers de configuration et ressources
└── test/                    # Classes de test
```

## Contribution

1. Forkez le dépôt
2. Créez une branche de fonctionnalité (`git checkout -b feature/FonctionnalitéIncroyable`)
3. Commitez vos modifications (`git commit -m 'Ajout de la FonctionnalitéIncroyable'`)
4. Poussez vers la branche (`git push origin feature/FonctionnalitéIncroyable`)
5. Ouvrez une Pull Request

## Licence

Ce projet est sous licence MIT - voir le fichier [LICENSE](../LICENSE) pour plus de détails.

## Support

Pour obtenir de l'aide, veuillez ouvrir une issue dans le dépôt GitHub ou contacter l'équipe de développement.