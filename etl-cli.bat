@echo off
REM ETL CLI Command Runner for Windows
REM Usage: etl-cli.bat <command> [options]

setlocal enabledelayedexpansion

echo ETL CLI - Starting...

REM Auto-detect JAVA_HOME if not set
if "%JAVA_HOME%"=="" (
    echo JAVA_HOME not set, attempting to detect Java installation...

    REM Try common Java locations
    for %%i in (
        "C:\Program Files\Java\jdk*"
        "C:\Program Files\OpenJDK\jdk*"
        "C:\Program Files\Eclipse Adoptium\jdk*"
        "%ProgramFiles%\Java\jdk*"
        "%ProgramFiles(x86)%\Java\jdk*"
    ) do (
        for /d %%j in (%%i) do (
            if exist "%%j\bin\java.exe" (
                set "JAVA_HOME=%%j"
                echo Found Java at: !JAVA_HOME!
                goto :java_found
            )
        )
    )

    REM If still not found, try to use java from PATH
    where java >nul 2>&1
    if !errorlevel! equ 0 (
        echo Using Java from PATH
        goto :java_found
    )

    echo Error: Java is not installed or not in PATH
    echo Please install Java 17 or higher and set JAVA_HOME
    echo Or ensure java.exe is in your PATH
    exit /b 1
)

:java_found
REM Test Java installation
"%JAVA_HOME%\bin\java.exe" -version >nul 2>&1 || java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java installation test failed
    echo Please check your Java installation
    exit /b 1
)

echo Java: OK

REM Set the JAR file path
set JAR_FILE=target\etl-0.0.1-SNAPSHOT.jar

REM Check if JAR file exists
if not exist "%JAR_FILE%" (
    echo Error: JAR file not found at %JAR_FILE%
    echo.
    echo To build the project, run one of:
    echo   build.bat                    ^(automated build^)
    echo   make build                   ^(if you have make^)
    echo   mvnw clean package -DskipTests  ^(manual build^)
    echo.
    exit /b 1
)

echo JAR file found: %JAR_FILE%

REM Run the ETL CLI command
echo Running ETL with arguments: %*
if "%JAVA_HOME%"=="" (
    java -jar "%JAR_FILE%" %*
) else (
    "%JAVA_HOME%\bin\java.exe" -jar "%JAR_FILE%" %*
)

set EXIT_CODE=%errorlevel%
if %EXIT_CODE% neq 0 (
    echo ETL execution failed with exit code: %EXIT_CODE%
)

endlocal
exit /b %EXIT_CODE%
