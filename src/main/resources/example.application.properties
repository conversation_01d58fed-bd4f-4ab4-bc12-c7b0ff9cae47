spring.application.name=etl
spring.main.web-application-type=none

# MySQL Target Database Configuration
spring.datasource.url=***********************************************************************************************************
spring.datasource.username=
spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.default_catalog=ab_helios_preprod
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
# Use exact property/field names as column names (no camelCase to snake_case conversion)
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

# Disable Apache HttpClient and RestClient auto-configuration to avoid TlsSocketStrategy classpath issues
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration,org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration

# SQL Server (manual usage, not managed by Spring Data)
sqlserver.url=*****************************************************************************************
sqlserver.username=
sqlserver.password=
sqlserver.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# SQLite Progress Tracking Database
progress.datasource.url=*****************************
progress.datasource.driver-class-name=org.sqlite.JDBC
progress.datasource.username=
progress.datasource.password=

# Path of Helios files
helios.path.commandes=
helios.path.documents=