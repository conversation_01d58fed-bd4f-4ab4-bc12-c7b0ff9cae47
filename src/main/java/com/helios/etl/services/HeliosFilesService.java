package com.helios.etl.services;

import com.helios.etl.config.HeliosPathConfig;
import com.helios.etl.source.entities.HeliosPJ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Service
public class HeliosFilesService {
    private static final Logger log = LoggerFactory.getLogger(HeliosFilesService.class);
    private final HeliosPathConfig heliosPathConfig;

    public HeliosFilesService(HeliosPathConfig heliosPathConfig) {
        this.heliosPathConfig = heliosPathConfig;
    }

    public int getCommandesFilesCount() {
        return getFilesCount(heliosPathConfig.getCommandes());
    }

    public int getDocumentsFilesCount() {
        return getFilesCount(heliosPathConfig.getDocuments());
    }

    private int getFilesCount(String path) {
        try {
            Path filesPath = Paths.get(path);
            log.debug("Checking commandes directory: {}", filesPath);

            if (!Files.exists(filesPath) || !Files.isDirectory(filesPath)) {
                return 0;
            }

            int count = 0;
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(filesPath)) {
                for (Path entry : stream) {
                    count++;
                }
            }

            return count;
        } catch (IOException e) {
            log.error("Error counting files in commandes directory: {}", e.getMessage());
            return 0;
        }
    }

    public List<HeliosPJ> getCommandePieceJointes(String commande) {
        return getPieceJointes(heliosPathConfig.getCommandes(), commande, false);
    }

    public List<HeliosPJ> getCommandePieceJointesWithContent(String commande) {
        return getPieceJointes(heliosPathConfig.getCommandes(), commande, true);
    }

    public List<HeliosPJ> getDocumentPieceJointes(String document) {
        return getPieceJointes(heliosPathConfig.getDocuments(), document, false);
    }

    public List<HeliosPJ> getDocumentPieceJointesWithContent(String document) {
        return getPieceJointes(heliosPathConfig.getDocuments(), document, true);
    }

    private List<HeliosPJ> getPieceJointes(String basePath, String subDirectory, boolean includeContent) {
        List<HeliosPJ> heliosPJList = new ArrayList<>();
        try {
            Path targetPath = Paths.get(basePath, subDirectory);
            log.debug("Listing files in directory: {}", targetPath);

            if (!Files.exists(targetPath) || !Files.isDirectory(targetPath)) {
                return heliosPJList;
            }

            try (DirectoryStream<Path> stream = Files.newDirectoryStream(targetPath)) {
                for (Path entry : stream) {
                    if (Files.isRegularFile(entry)) {
                        HeliosPJ heliosPJ = createHeliosPJ(entry, includeContent);
                        if (heliosPJ != null) {
                            heliosPJList.add(heliosPJ);
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("Error listing files in directory: {}", e.getMessage());
        }
        return heliosPJList;
    }

    private HeliosPJ createHeliosPJ(Path entry, boolean includeContent) {
        try {
            HeliosPJ heliosPJ = new HeliosPJ();
            heliosPJ.setNom(entry.getFileName().toString());
            heliosPJ.setPath(entry.toAbsolutePath().toString());
            heliosPJ.setTaille(Files.size(entry));

            // Detect MIME type
            try {
                String mimeType = Files.probeContentType(entry);
                heliosPJ.setTypeMime(mimeType != null ? mimeType : "application/octet-stream");
            } catch (IOException e) {
                log.warn("Could not detect MIME type for {}: {}", entry, e.getMessage());
                heliosPJ.setTypeMime("application/octet-stream");
            }

            // Load content if requested
            if (includeContent) {
                try {
                    byte[] fileBytes = Files.readAllBytes(entry);
                    Byte[] contentArray = new Byte[fileBytes.length];
                    for (int i = 0; i < fileBytes.length; i++) {
                        contentArray[i] = fileBytes[i];
                    }
                    heliosPJ.setContenu(contentArray);
                } catch (IOException e) {
                    log.warn("Could not read file content for {}: {}", entry, e.getMessage());
                    heliosPJ.setContenu(new Byte[0]);
                }
            }

            return heliosPJ;

        } catch (IOException e) {
            log.warn("Could not process file {}: {}", entry, e.getMessage());
            return null;
        }
    }
}