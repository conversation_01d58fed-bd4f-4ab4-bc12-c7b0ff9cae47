package com.helios.etl.services.validators;

import com.helios.etl.helper.StringHelper;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.source.helper.DefaultHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

public class TicketValidator {
    private static final Logger log = LoggerFactory.getLogger(TicketValidator.class);

    public static boolean IsTicketValid(Tickets ticket) {
        if (ticket == null) {
            log.warn("Ticket validation failed: ticket is null");
            return false;
        }

        // Validate required ID
        if (ticket.getIdTickets() <= 0) {
            return false;
        }

        // Validate required string fields are not null or empty
        if (StringHelper.isNullOrEmpty(ticket.getCtNum())) {
            return false;
        }

        if (StringHelper.isNullOrEmpty(ticket.getClient())) {
            return false;
        }

        if (StringHelper.isNullOrEmpty(ticket.getTitre())) {
            return false;
        }

        if (StringHelper.isNullOrEmpty(ticket.getStatus())) {
            return false;
        }

        // Validate date fields are not DefaultHelper.DEFAULT_MIN_DATE (which indicates uninitialized)
        if (ticket.getDateCreation() == null || ticket.getDateCreation().equals(DefaultHelper.DEFAULT_MIN_DATE)) {
            return false;
        }

        // Validate business logic constraints
        if (ticket.getDateCreation().isAfter(LocalDateTime.now().plusDays(1))) {
            return false;
        }

        // Validate resolution date is not before creation date (if set)
        if (ticket.getDateResolution() != null &&
                !ticket.getDateResolution().equals(DefaultHelper.DEFAULT_MIN_DATE) &&
                ticket.getDateResolution().isBefore(ticket.getDateCreation())) {
            return false;
        }

        // Validate niveau is within reasonable range
        if (ticket.getNiveau() < 0 || ticket.getNiveau() > 2) {
            return false;
        }

        // Validate tempsTotal is not negative
        if (ticket.getTempsTotal() < 0) {
            return false;
        }

        // Validate string field lengths to prevent database truncation issues
        if (ticket.getTitre() != null && ticket.getTitre().length() > 255) {
            return false;
        }

        if (ticket.getDescription() != null && ticket.getDescription().length() > 5000) {
            log.warn("Ticket validation failed: description exceeds maximum length for ticket {}", ticket.getIdTickets());
            return false;
        }

        log.debug("Ticket validation successful for ticket {} with ctNum {}",
                ticket.getIdTickets(), ticket.getCtNum());
        return true;
    }
}
