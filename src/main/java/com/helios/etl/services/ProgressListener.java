package com.helios.etl.services;

/**
 * Simple progress listener for reporting ETL progress to a UI/CLI
 */
public interface ProgressListener {
    /**
     * Called to report progress.
     * @param total total number of items to process
     * @param current current processed count (1-based)
     * @param note short note such as OK/ERR or any small status
     */
    void onProgress(int total, int current, String note);
}
