package com.helios.etl.services.transformers;

import com.helios.etl.helper.DescriptionHelper;
import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Projet;
import com.helios.etl.model.TypeProjet;
import com.helios.etl.services.CacheMemory;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class TypeToProjetTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public TypeToProjetTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Projet transform(String pole, String type) {
        Projet Projet = TryGetExistingProjet(pole, type);
        if(Projet != null)
        {
            return Projet;
        }

        Projet = new Projet();

        TypeProjet typeProjet = _cm.getHelper().tryGetTypeProjetFromPoleAndType(pole, type);
        if(typeProjet == null)
        {
            typeProjet = new TypeProjet();
            // Set a unique code based on pole and type to avoid constraint violations
            String code = generateUniqueCode(pole, type, "PROJET");
            typeProjet.setCode(code);
            typeProjet.setLibelle(type);
            typeProjet.setDescription(DescriptionHelper.GetTicketTypeDescription(type));

            try {
                typeProjet = _cm.getTypeProjetRepository().save(typeProjet);
                _cm.getTypeProjets().add(typeProjet);

                // Associate the TypeProjet with the appropriate DomaineMetier
                associateTypeProjetWithDomaine(typeProjet, pole);

            } catch (Exception e) {
                // Log the error and try to find an existing one with the same libelle
                System.err.println("Failed to save TypeProjet with code " + code + ": " + e.getMessage());
                // Try to find existing TypeProjet by libelle as fallback
                typeProjet = _cm.getTypeProjets().stream()
                    .filter(tp -> type.equals(tp.getLibelle()))
                    .findFirst()
                    .orElse(null);
                if (typeProjet == null) {
                    throw new RuntimeException("Failed to create or find TypeProjet for type: " + type, e);
                }
            }
        }


        String defaultDescriptionProjet = "[Description par defaut] " + typeProjet.getDescription();
        // Set a unique code for the Projet to avoid constraint violations
        String projetCode = generateUniqueProjetCode(pole, type);
        Projet.setCode(projetCode);
        Projet.setLibelle(type);
        Projet.setDescription(defaultDescriptionProjet);
        Projet.setType(typeProjet);

        // Get the DomaineMetier from the pole - this is required for domainePrincipal
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
        DomaineMetier domainePrincipal = ptdmt.transform(pole);

        if (domainePrincipal == null) {
            throw new RuntimeException("Failed to get or create DomaineMetier for pole: " + pole + ". Cannot create Projet without domainePrincipal.");
        }

        // Set the domainePrincipal (required field)
        Projet.setDomainePrincipal(domainePrincipal);

        // Try to get additional domains from TypeProjet, but domainePrincipal is the minimum requirement
        Set<DomaineMetier> domaines = new HashSet<>();
        // Always include the principal domain
        if (domainePrincipal != null) {
            domaines.add(domainePrincipal);
        }

        try {
            if (typeProjet.getDomainesMetier() != null && !typeProjet.getDomainesMetier().isEmpty()) {
                // Add domains ensuring uniqueness by OID to avoid duplicate join-table inserts
                for (DomaineMetier dm : typeProjet.getDomainesMetier()) {
                    boolean exists = domaines.stream().anyMatch(x -> x != null && dm != null && x.getOid() == dm.getOid());
                    if (!exists) {
                        domaines.add(dm);
                    }
                }
            }
        } catch (Exception e) {
            // Handle lazy loading exception gracefully
            System.err.println("Warning: Could not access domainesMetier for TypeProjet " + typeProjet.getLibelle() + ": " + e.getMessage());
            // Continue with just the principal domain
        }
        Projet.setDomaines(domaines);
        Projet.setDateCreation(LocalDateTime.now());
        Projet.setDateModification(LocalDateTime.now());

        try {
            Projet = _cm.getProjetRepository().save(Projet);
            _cm.getProjets().add(Projet);
        } catch (Exception e) {
            // Log the error and try to find an existing one
            System.err.println("Failed to save Projet with code " + projetCode + ": " + e.getMessage());
            // Try to find existing Projet by type and pole as fallback
            Projet = TryGetExistingProjet(pole, type);
            if (Projet == null) {
                throw new RuntimeException("Failed to create or find Projet for pole: " + pole + ", type: " + type, e);
            }
        }

        return Projet;
    }

    private Projet TryGetExistingProjet(String pole, String type)
    {
        for(Projet m : _cm.getProjets())
        {
            if(Objects.equals(m.getType().getLibelle(), type) &&
                (
                    Objects.equals(m.getDomainePrincipal().getLibelle(), pole) ||
                    _cm.getHelper().HasDomaineMetierFromPole(m.getDomaines(), pole)
                ))
            {
                return m;
            }
        }

        return null;
    }

    /**
     * Generate a unique code for TypeProjet based on pole and type
     * @param pole the pole name
     * @param type the type name
     * @param prefix the prefix to use
     * @return a unique code string
     */
    private String generateUniqueCode(String pole, String type, String prefix) {
        // Clean and normalize the inputs
        String cleanPole = pole != null ? pole.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";
        String cleanType = type != null ? type.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";

        // Limit length to avoid exceeding database column constraints (50 chars max)
        if (cleanPole.length() > 10) cleanPole = cleanPole.substring(0, 10);
        if (cleanType.length() > 15) cleanType = cleanType.substring(0, 15);

        // Generate base code
        String baseCode = prefix + "_" + cleanPole + "_" + cleanType;

        // Ensure it doesn't exceed 50 characters
        if (baseCode.length() > 45) {
            baseCode = baseCode.substring(0, 45);
        }

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (codeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;
            // Ensure we don't exceed 50 chars with the counter
            if (finalCode.length() > 50) {
                baseCode = baseCode.substring(0, 47 - String.valueOf(counter).length());
                finalCode = baseCode + "_" + counter;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the TypeProjet cache
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean codeExists(String code) {
        return _cm.getTypeProjets().stream()
                .anyMatch(tp -> code.equals(tp.getCode()));
    }

    /**
     * Generate a unique code for Projet based on pole and type
     * @param pole the pole name
     * @param type the type name
     * @return a unique code string
     */
    private String generateUniqueProjetCode(String pole, String type) {
        // Clean and normalize the inputs
        String cleanPole = pole != null ? pole.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";
        String cleanType = type != null ? type.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";

        // Limit length to avoid exceeding database column constraints (50 chars max)
        if (cleanPole.length() > 10) cleanPole = cleanPole.substring(0, 10);
        if (cleanType.length() > 15) cleanType = cleanType.substring(0, 15);

        // Generate base code
        String baseCode = "P_" + cleanPole + "_" + cleanType;

        // Ensure it doesn't exceed 50 characters
        if (baseCode.length() > 45) {
            baseCode = baseCode.substring(0, 45);
        }

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (projetCodeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;
            // Ensure we don't exceed 50 chars with the counter
            if (finalCode.length() > 50) {
                baseCode = baseCode.substring(0, 47 - String.valueOf(counter).length());
                finalCode = baseCode + "_" + counter;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the Projet cache
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean projetCodeExists(String code) {
        return _cm.getProjets().stream()
                .anyMatch(p -> code.equals(p.getCode()));
    }

    /**
     * Associate a TypeProjet with the appropriate DomaineMetier based on pole
     * @param typeProjet the TypeProjet to associate
     * @param pole the pole name to find the corresponding DomaineMetier
     */
    private void associateTypeProjetWithDomaine(TypeProjet typeProjet, String pole) {
        try {
            // Get or create the DomaineMetier for this pole
            PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
            DomaineMetier domaineMetier = ptdmt.transform(pole);

            if (domaineMetier != null) {
                // Initialize the collection if it's null
                if (typeProjet.getDomainesMetier() == null) {
                    typeProjet.setDomainesMetier(new ArrayList<>());
                }

                // Add the domain if it's not already present
                if (!typeProjet.getDomainesMetier().contains(domaineMetier)) {
                    typeProjet.getDomainesMetier().add(domaineMetier);
                    // Save the updated TypeProjet
                    _cm.getTypeProjetRepository().save(typeProjet);
                    System.out.println("Associated TypeProjet " + typeProjet.getLibelle() + " with DomaineMetier " + domaineMetier.getLibelle());
                }
            } else {
                System.err.println("Warning: Could not get or create DomaineMetier for pole: " + pole);
            }
        } catch (Exception e) {
            System.err.println("Warning: Failed to associate TypeProjet with DomaineMetier for pole " + pole + ": " + e.getMessage());
        }
    }
}
