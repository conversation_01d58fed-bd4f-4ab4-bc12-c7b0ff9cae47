package com.helios.etl.services.transformers;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Mission;
import com.helios.etl.model.TypeMission;
import com.helios.etl.source.entities.Categorie;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;

import java.util.HashSet;

public class CategorieToMissionTransformer {
    private EntityManager em;

    public CategorieToMissionTransformer(EntityManager em) {
        this.em = em;
    }

    public Mission transform(Categorie categorie) {

        return null;
    }

    private String GetDescriptionForMission(Categorie categorie) {
        return "";
    }

    private Mission findMissionByLibelle(String libelle) {
        try {
            return em.createQuery(
                            "SELECT m FROM Mission m WHERE m.libelle = :libelle", Mission.class)
                    .setParameter("libelle", libelle)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null; // No mission found with this libelle
        }
    }


    private TypeMission findTypeMissionByLibelle(String libelle) {
        try {
            return em.createQuery(
                            "SELECT tm FROM TypeMission tm WHERE tm.libelle = :libelle", TypeMission.class)
                    .setParameter("libelle", libelle)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null; // No TypeMission found with this libelle
        }
    }
}
