package com.helios.etl.services.transformers;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Personne;
import com.helios.etl.model.TypePersonne;
import com.helios.etl.outer.model.SourceOfData;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Contact;
import com.helios.etl.source.entities.Utilisateurs;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;

public class ContactToPersonneTransformer {
    private CacheMemory _cm = null;

    public ContactToPersonneTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    @Transactional
    public Personne transform(Contact contact) {
        if (!validateContact(contact)) {
            return null;
        }

        Personne personne = tryGetPersonneFromCache(contact);
        if (personne != null) {
            return personne;
        }

        personne = new Personne();
        personne.setNom(contact.getNom().toUpperCase());

        String capitalizedPrenom = contact.getPrenom().substring(0, 1).toUpperCase() + contact.getPrenom().substring(1).toLowerCase();
        personne.setPrenom(capitalizedPrenom);
        personne.setEmail(contact.getEmail());
        personne.setTelephone(contact.getTelephone());
        personne.setFonction(contact.getFonction());
        boolean isInterne = contact.getCtNum().trim().equalsIgnoreCase("AB");

        // Set required ExternalEntity fields
        personne.setExterne(true);

        // Initialize external source information
        SourceOfData externalSource = new SourceOfData();
        externalSource.setOid((long) contact.getId());
        externalSource.setKind("Contact");
        externalSource.setName("Helios1_Contact");
        externalSource.setDateUpdated(LocalDateTime.now());
        personne.setExternalSource(externalSource);

        // Les contacts n'ont pas de DomaineMetier, on utilise un set vide
        HashSet<DomaineMetier> domaineMetiers = new HashSet<>();

        String expectedCode = "CONTACT_" + (isInterne ? "INT" : "EXT");
        TypePersonne type = tryGetTypePersonneFromCacheByCode(expectedCode);
        boolean toSaveType = false;
        if(type == null)
        {
            type = new TypePersonne();
            // Use shared TypePersonne per interne flag (not unique per contact)
            String code = expectedCode;
            type.setCode(code);
            type.setLibelle("Contact " + (isInterne ? "Interne" : "Externe"));
            type.setInterne(isInterne);

            if(!domaineMetiers.isEmpty()) {
                type.setDomainesMetier(domaineMetiers);
            }
            toSaveType = true;
        }

        if(toSaveType) {
            try {
                type = _cm.getTypePersonneRepository().save(type);
                // Do not force flush here; let transaction boundaries handle persistence
                // Verify the TypePersonne was saved with a valid OID
                if (type.getOid() <= 0) {
                    throw new RuntimeException("TypePersonne was saved but has invalid OID: " + type.getOid());
                }
                _cm.getTypePersonnes().add(type);
                System.out.println("Successfully saved TypePersonne with OID: " + type.getOid() + " and code: " + type.getCode());
            } catch (Exception e) {
                // Log the error and try to find an existing one or create with different code
                System.err.println("Failed to save TypePersonne with code " + type.getCode() + ": " + e.getMessage());
                e.printStackTrace();

                // First, try to find existing TypePersonne by deterministic code in cache
                type = _cm.getTypePersonnes().stream()
                    .filter(tp -> expectedCode.equals(tp.getCode()))
                    .findFirst()
                    .orElse(null);

                // If not in cache, try DB lookup by unique code
                if (type == null) {
                    try {
                        type = _cm.getTypePersonneRepository().findByCode(expectedCode).orElse(null);
                        if (type != null) {
                            _cm.getTypePersonnes().add(type);
                            System.out.println("Using existing TypePersonne from DB with OID: " + type.getOid());
                        }
                    } catch (Exception findEx) {
                        System.err.println("Warning: lookup TypePersonne by code failed: " + findEx.getMessage());
                    }
                }

                if (type != null && type.getOid() > 0) {
                    System.out.println("Using existing TypePersonne with OID: " + type.getOid());
                } else {
                    // If no existing one found, try to create with a timestamp-based unique code
                    try {
                        type = new TypePersonne();
                        String uniqueCode = expectedCode + "_" + (System.currentTimeMillis() % 100000);
                        type.setCode(uniqueCode);
                        type.setLibelle("Contact " + (isInterne ? "Interne" : "Externe"));
                        type.setInterne(isInterne);

                        type = _cm.getTypePersonneRepository().save(type);

                        if (type.getOid() <= 0) {
                            throw new RuntimeException("TypePersonne was saved but has invalid OID: " + type.getOid());
                        }

                        _cm.getTypePersonnes().add(type);
                        System.out.println("Created new TypePersonne with unique code: " + uniqueCode + " and OID: " + type.getOid());
                    } catch (Exception e2) {
                        throw new RuntimeException("Failed to create or find valid TypePersonne for contact", e2);
                    }
                }
            }
        }

        personne.setData(contact.toMap());

        // Validate TypePersonne has valid OID before assignment
        if (type == null) {
            throw new RuntimeException("TypePersonne is null for contact " + contact.getNom() + " " + contact.getPrenom());
        }
        if (type.getOid() <= 0) {
            throw new RuntimeException("TypePersonne has invalid OID (" + type.getOid() + ") for contact " + contact.getNom() + " " + contact.getPrenom());
        }

        personne.setType(type);

        try {
            // Validate all required fields before attempting save
            validatePersonneBeforeSave(personne, contact);

            personne = _cm.getPersonneRepository().save(personne);
            // Do not force flush here; let transaction commit handle persistence
            // Verify the Personne was saved with a valid OID
            if (personne.getOid() <= 0) {
                throw new RuntimeException("Personne was saved but has invalid OID: " + personne.getOid());
            }
            _cm.getPersonnes().add(personne);
            System.out.println("Successfully saved Personne with OID: " + personne.getOid() + " for contact " + contact.getNom() + " " + contact.getPrenom());
        } catch (Exception e) {
            System.err.println("Failed to save Personne for contact " + contact.getNom() + " " + contact.getPrenom() + ": " + e.getMessage());
            System.err.println("TypePersonne OID: " + (type != null ? type.getOid() : "null"));
            System.err.println("Personne details: nom=" + personne.getNom() + ", prenom=" + personne.getPrenom() + ", email=" + personne.getEmail());
            e.printStackTrace();

            // Don't throw exception immediately - try to find existing Personne as fallback
            personne = findExistingPersonne(contact);
            if (personne == null) {
                throw new RuntimeException("Failed to create or find Personne for contact " + contact.getNom() + " " + contact.getPrenom(), e);
            }
            System.out.println("Using existing Personne with OID: " + personne.getOid());
        }

        return personne;
    }

    public HashSet<Personne> transformContactsToPersonnes(HashSet<Contact> contacts) {
        HashSet<Personne> personnes = new HashSet<>();
        for (Contact contact : contacts) {
            Personne personne = this.transform(contact);
            if (personne != null) {
                personnes.add(personne);
            }
        }
        return personnes;
    }

    private TypePersonne tryGetTypePersonneFromCache(Contact contact, boolean isInterne) {
        if (_cm == null || _cm.getTypePersonnes() == null) {
            return null;
        }

        // Legacy search by libelle (kept for backward compatibility)
        for (TypePersonne type : _cm.getTypePersonnes()) {
            if (type.isInterne() == isInterne && type.getLibelle() != null &&
                type.getLibelle().contains("Contact")) {
                return type;
            }
        }

        return null;
    }

    private TypePersonne tryGetTypePersonneFromCacheByCode(String code) {
        if (_cm == null || _cm.getTypePersonnes() == null || code == null) {
            return null;
        }
        for (TypePersonne type : _cm.getTypePersonnes()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return null;
    }

    /**
     * Generate a unique code for TypePersonne based on type and interne flag
     * @param typePrefix the type prefix (e.g., "CONTACT", "USER")
     * @param isInterne whether the person is internal
     * @return a unique code string
     */
    private String generateUniqueTypePersonneCode(String typePrefix, boolean isInterne) {
        // Generate base code
        String baseCode = typePrefix + "_" + (isInterne ? "INT" : "EXT");

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (typePersonneCodeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;

            // Safety check to prevent infinite loop
            if (counter > 1000) {
                // Use timestamp as last resort
                finalCode = typePrefix + "_" + System.currentTimeMillis() % 100000;
                break;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the TypePersonne cache or database
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean typePersonneCodeExists(String code) {
        // Check in-memory cache only to avoid triggering a flush during generation
        boolean existsInCache = _cm.getTypePersonnes().stream()
                .anyMatch(tp -> code.equals(tp.getCode()));

        if (existsInCache) {
            return true;
        }
        // Do not query the database here; if a duplicate exists, DB constraints will handle it at save time.
        return false;
    }

    private boolean validateContact(Contact contact) {
        if (contact == null) {
            return false;
        }
        if (contact.getNom() == null || contact.getNom().isEmpty()) {
            return false;
        }
        if (contact.getPrenom() == null || contact.getPrenom().isEmpty()) {
            return false;
        }

        contact.setNom(contact.getNom().toUpperCase());
        String capitalizedPrenom = contact.getPrenom().substring(0, 1).toUpperCase() + contact.getPrenom().substring(1).toLowerCase();
        contact.setPrenom(capitalizedPrenom);

        if (contact.getEmail() == null) {
            contact.setEmail("");
        }
        if (contact.getTelephone() == null) {
            contact.setTelephone("");
        }
        if (contact.getTelephone2() == null) {
            contact.setTelephone2("");
        }
        if (contact.getTelephone3() == null) {
            contact.setTelephone3("");
        }
        if (contact.getCivilite() == null) {
            contact.setCivilite("");
        }
        if (contact.getCtNo() == null || contact.getCtNo().isEmpty()) {
            contact.setCtNo("N/A");
        }
        if (contact.getCtNum() == null || contact.getCtNum().isEmpty()) {
            contact.setCtNum("N/A");
        }
        if (contact.getDateCreation() == null) {
            contact.setDateCreation(LocalDateTime.now());
        }
        if (contact.getDateModification() == null) {
            contact.setDateModification(LocalDateTime.now());
        }
        if (contact.getFonction() == null)
        {
            contact.setFonction("Inconnue");
        }
        if (contact.getMobile() == null) {
            contact.setMobile("");
        }
        if (contact.getNote() == null) {
            contact.setNote("");
        }
        return true;
    }

    private Personne tryGetPersonneFromCache(Contact c) {
        if (_cm == null || _cm.getPersonnes() == null) {
            return null;
        }

        for (Personne p : _cm.getPersonnes()) {
            if (p.getNom().equalsIgnoreCase(c.getNom()) &&
                    p.getPrenom().equalsIgnoreCase(c.getPrenom())) {
                return p;
            }
        }

        return null;
    }

    /**
     * Validate Personne entity before attempting to save
     * @param personne the Personne to validate
     * @param contact the source Contact
     * @throws RuntimeException if validation fails
     */
    private void validatePersonneBeforeSave(Personne personne, Contact contact) {
        if (personne.getNom() == null || personne.getNom().trim().isEmpty()) {
            throw new RuntimeException("Personne nom is null or empty for contact " + contact.getId());
        }
        if (personne.getPrenom() == null || personne.getPrenom().trim().isEmpty()) {
            throw new RuntimeException("Personne prenom is null or empty for contact " + contact.getId());
        }
        if (personne.getEmail() == null) {
            throw new RuntimeException("Personne email is null" + contact.getId());
        }
        if (personne.getTelephone() == null) {
            personne.setTelephone("");
        }
        if (personne.getFonction() == null) {
            throw new RuntimeException("Personne fonction is null" + contact.getId());
        }
        if (personne.getType() == null) {
            throw new RuntimeException("Personne type is null for contact " + contact.getId());
        }
        if (personne.getType().getOid() <= 0) {
            throw new RuntimeException("Personne type has invalid OID for contact " + contact.getId());
        }
        if (personne.getExternalSource() == null) {
            throw new RuntimeException("Personne externalSource is null for contact " + contact.getId());
        }
    }

    /**
     * Try to find an existing Personne for the given contact
     * @param contact the source Contact
     * @return existing Personne or null if not found
     */
    private Personne findExistingPersonne(Contact contact) {
        try {
            return _cm.getPersonnes().stream()
                .filter(p -> p.getExternalSource() != null &&
                           p.getExternalSource().getOid() != null &&
                           p.getExternalSource().getOid().equals((long) contact.getId()) &&
                           "Contact".equals(p.getExternalSource().getKind()))
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            System.err.println("Warning: Could not search for existing Personne: " + e.getMessage());
            return null;
        }
    }
}
