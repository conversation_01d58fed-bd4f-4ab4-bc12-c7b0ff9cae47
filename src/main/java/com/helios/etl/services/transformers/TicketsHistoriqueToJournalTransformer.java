package com.helios.etl.services.transformers;

import com.helios.etl.model.AbstractIssue;
import com.helios.etl.model.Journal;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.TicketsHistorique;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;

public class TicketsHistoriqueToJournalTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public TicketsHistoriqueToJournalTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Journal transform(TicketsHistorique historique, AbstractIssue issue) {
        if (historique == null || historique.getIdHistorique() == 0 || issue == null) {
            return null;
        }

        Journal journal = new Journal();
        journal.setDateCreation(historique.getDateModification());
        journal.setNote(historique.getDescription());
        journal.setIssue(issue);
        journal.setInterne(historique.getNoteInterne() > 0);
        journal.setPrive(false);

        return journal;
    }

    public HashSet<Journal> transformCollection(HashSet<TicketsHistorique> historiques, AbstractIssue issue) {
        if (issue == null) {
            return new HashSet<>();
        }
        if (historiques == null || historiques.isEmpty()) {
            return new HashSet<>();
        }

        HashSet<Journal> journaux = new HashSet<>();
        for(TicketsHistorique th : historiques)
        {
            Journal j = transform(th, issue);
            if(j != null)
            {
                journaux.add(j);
            }
        }

        return journaux;
    }
}
