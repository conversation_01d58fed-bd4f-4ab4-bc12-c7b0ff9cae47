package com.helios.etl.utils;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.TypeMission;
import com.helios.etl.model.TypeProjet;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Utilisateurs;

import java.util.Collection;
import java.util.Objects;

public class CacheMemoryHelper {

    private CacheMemory cacheMemory;

    public CacheMemoryHelper(CacheMemory cacheMemory) {
        this.cacheMemory = cacheMemory;
    }


    /**
     * Try to get a TypeMission from the cache memory based on the pole and type.
     * If not found, returns null.
     * @param pole
     * @param type
     * @return
     */
    public TypeMission tryGetTypeMissionFromPoleAndType(String pole, String type)
    {
        if(cacheMemory == null)
            return null;

        for(TypeMission typeMission : cacheMemory.getTypeMissions())
        {
            try {
                if(Objects.equals(typeMission.getLibelle(), type) && HasDomaineMetierFromPole(typeMission.getDomainesMetier(), pole))
                {
                    return typeMission;
                }
            } catch (Exception e) {
                // Handle lazy loading exception gracefully
                System.err.println("Warning: Could not access domainesMetier for TypeMission " + typeMission.getLibelle() + " during cache lookup: " + e.getMessage());
                // Try to match by libelle only as fallback
                if(Objects.equals(typeMission.getLibelle(), type)) {
                    System.err.println("Returning TypeMission " + typeMission.getLibelle() + " based on libelle match only (ignoring pole check due to lazy loading issue)");
                    return typeMission;
                }
            }
        }

        return null;
    }

    /**
     * Check if there is a DomaineMetier in the collection that matches the given pole.
     * This is case-insensitive.
     * @param domainesMetiers
     * @param pole
     * @return
     */
    public boolean HasDomaineMetierFromPole(Collection<DomaineMetier> domainesMetiers, String pole)
    {
        if (cacheMemory == null) return false;
        if (domainesMetiers == null || domainesMetiers.isEmpty()) return false;
        if (pole == null || pole.isBlank()) return false;

        for (DomaineMetier domaineMetier : domainesMetiers)
        {
            if (domaineMetier == null) continue;
            String libelle = domaineMetier.getLibelle();
            if (libelle != null && libelle.equalsIgnoreCase(pole))
                return true;
        }

        return false;
    }

    /**
     * Try to get a TypeProjet from the cache memory based on the pole and type.
     * If not found, returns null.
     * @param pole
     * @param type
     * @return
     */
    public TypeProjet tryGetTypeProjetFromPoleAndType(String pole, String type) {
        if(cacheMemory == null)
            return null;

        for(TypeProjet typeProjet : cacheMemory.getTypeProjets())
        {
            try {
                if(Objects.equals(typeProjet.getLibelle(), type) && HasDomaineMetierFromPole(typeProjet.getDomainesMetier(), pole))
                {
                    return typeProjet;
                }
            } catch (Exception e) {
                // Handle lazy loading exception gracefully
                System.err.println("Warning: Could not access domainesMetier for TypeProjet " + typeProjet.getLibelle() + " during cache lookup: " + e.getMessage());
                // Try to match by libelle only as fallback
                if(Objects.equals(typeProjet.getLibelle(), type)) {
                    System.err.println("Returning TypeProjet " + typeProjet.getLibelle() + " based on libelle match only (ignoring pole check due to lazy loading issue)");
                    return typeProjet;
                }
            }
        }

        return null;
    }

    public Utilisateurs tryGetUtilisateurById(int id) {
        if (cacheMemory == null || cacheMemory.getUtilisateurs() == null) {
            return null;
        }

        for (Utilisateurs utilisateur : cacheMemory.getUtilisateurs()) {
            if (utilisateur.getIdUtilisateur() == id) {
                return utilisateur;
            }
        }

        return null;
    }

    public Utilisateurs tryGetUtilisateurByUsername(String username) {
        if (cacheMemory == null || cacheMemory.getUtilisateurs() == null || username == null || username.isEmpty()) {
            return null;
        }

        for (Utilisateurs utilisateur : cacheMemory.getUtilisateurs()) {
            if (Objects.equals(utilisateur.getUtilisateur(), username) ||
                Objects.equals(utilisateur.getNom() + " " + utilisateur.getPrenom(), username)) {
                return utilisateur;
            }
        }

        return null;
    }
}
