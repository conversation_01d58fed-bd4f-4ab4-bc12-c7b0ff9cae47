package com.helios.etl.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableJpaRepositories(
    basePackages = "com.helios.etl.progress.repositories",
    entityManagerFactoryRef = "progressEntityManagerFactory",
    transactionManagerRef = "progressTransactionManager"
)
public class ProgressDataSourceConfig {

    @Bean(name = "progressDataSource")
    @ConfigurationProperties("progress.datasource")
    public DataSource progressDataSource() {
        return DataSourceBuilder.create()
                .driverClassName("org.sqlite.JDBC")
                .url("*****************************")
                .username("")
                .password("")
                .build();
    }

    @Bean(name = "progressEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean progressEntityManagerFactory(
            @Qualifier("progressDataSource") DataSource dataSource) {
        
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("com.helios.etl.progress.entities");
        
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "update");
        properties.put("hibernate.dialect", "org.hibernate.community.dialect.SQLiteDialect");
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.connection.characterEncoding", "utf8");
        properties.put("hibernate.connection.CharSet", "utf8");
        // SQLite-specific settings for better transaction handling
        properties.put("hibernate.connection.autocommit", "false");
        properties.put("hibernate.connection.isolation", "2"); // READ_COMMITTED
        properties.put("hibernate.jdbc.batch_size", "0"); // Disable batching for SQLite
        em.setJpaPropertyMap(properties);
        
        return em;
    }

    @Bean(name = "progressTransactionManager")
    public PlatformTransactionManager progressTransactionManager(
            @Qualifier("progressEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
