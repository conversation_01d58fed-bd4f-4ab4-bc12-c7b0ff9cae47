/**
 * 
 */
package com.helios.etl.outer.spring.data;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.domain.Page;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public class Pagination<T> {

	/**
	 * <AUTHOR>
	 *
	 */
	public class PageItem {

		/**
		 * 
		 */
		@Getter
		private int number;

		/**
		 * 
		 */
		@Getter
		private int name;

		/**
		 * 
		 */
		@Getter
		private boolean current;

		/**
		 * @param number
		 * @param current
		 */
		public PageItem(int number, boolean current) {
			this.number = number;
			this.name = number + 1;
			this.current = current;
		}
	}

	/**
	 * 
	 */
	public static final int MAX_PAGE_ITEM_DISPLAY = 5;

	/**
	 * 
	 */
	private Page<T> page;

	/**
	 * 
	 */
	@Getter
	private List<PageItem> items;

	/**
	 * 
	 */
	@Getter
	private int currentNumber;

	/**
	 * 
	 */
	@Getter
	private int nextNumber;

	/**
	 * 
	 */
	@Getter
	private int previousNumber;

	/**
	 * 
	 */
	@Getter
	private int lastNumber;

	/**
	 * 
	 */
	@Getter
	private int firstNumber;

	/**
	 * 
	 */
	@Getter
	private String endpoint;

	/**
	 * @param page
	 * @param endpoint
	 */
	public Pagination(Page<T> page, String endpoint) {

		this.page = page;
		this.endpoint = endpoint;
		items = new ArrayList<PageItem>();

		currentNumber = page.getNumber();
		firstNumber = 0;
		lastNumber = page.getTotalPages() - 1;
		previousNumber = currentNumber == 0 ? 0 : currentNumber - 1;
		nextNumber = currentNumber == lastNumber ? lastNumber : currentNumber + 1;

		int start, size;

		if (page.getTotalPages() <= MAX_PAGE_ITEM_DISPLAY) {

			start = 0;
			size = page.getTotalPages();

		} else if (currentNumber <= (MAX_PAGE_ITEM_DISPLAY - MAX_PAGE_ITEM_DISPLAY / 2)) {

			start = 0;
			size = MAX_PAGE_ITEM_DISPLAY;

		} else if (currentNumber > (page.getTotalPages() - MAX_PAGE_ITEM_DISPLAY / 2)) {

			start = page.getTotalPages() - MAX_PAGE_ITEM_DISPLAY + 1;
			size = MAX_PAGE_ITEM_DISPLAY;

		} else {

			start = currentNumber - MAX_PAGE_ITEM_DISPLAY / 2;
			size = MAX_PAGE_ITEM_DISPLAY;

		}

		for (int i = 0; i < size; i++) {
			items.add(new PageItem(start + i, (start + i) == currentNumber));
		}

	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Page#getTotalPages()
	 */
	public int getTotalPages() {
		return page.getTotalPages();
	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Slice#getSize()
	 */
	public int getSize() {
		return page.getSize();
	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Slice#getContent()
	 */
	public List<T> getContent() {
		return page.getContent();
	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Slice#isFirst()
	 */
	public boolean isFirst() {
		return page.isFirst();
	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Slice#isLast()
	 */
	public boolean isLast() {
		return page.isLast();
	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Slice#hasNext()
	 */
	public boolean hasNext() {
		return page.hasNext();
	}

	/**
	 * @return
	 * @see org.springframework.data.domain.Slice#hasPrevious()
	 */
	public boolean hasPrevious() {
		return page.hasPrevious();
	}

}
