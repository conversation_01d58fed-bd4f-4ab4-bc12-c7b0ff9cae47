/**
 * 
 */
package com.helios.etl.outer.spring.hibernate;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.UserType;

/**
 * JPA and Hibernate provides two ways to map enum fields to database fields:
 * either map the enum value ordinal with @Enumerated(EnumType.ORDINAL) or the
 * enum value name with @Enumerated(EnumType.STRING) . Both cases are not ideal,
 * because it's very easy to make changes to the enum, such as changing values
 * order or renaming values, and forgetting to migrate the existing values in
 * the database accordingly.
 * 
 * Actually the need to migrate existing data is not justified if you just want
 * to change Java code. Here is a mechanism that lets you decouple the enum
 * value names and ordinals from the data, while still making it easy to map
 * enum fields to database columns.
 * 
 * <AUTHOR> - Original
 * <AUTHOR> - adaptation & Hibernate 6 version
 * @see http://www.gabiaxel.com/2011/01/better-enum-mapping-with-hibernate.html
 *
 */
@SuppressWarnings("rawtypes")
public abstract class PersistentEnumUserType<J, T extends PersistentEnum> implements UserType<T> {

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#isMutable()
	 */
	@Override
	public boolean isMutable() {
		return false;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#deepCopy(java.lang.Object)
	 */
	@Override
	public T deepCopy(T value) throws HibernateException {
		return value; // PersistentEnum is immutable
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#replace(java.lang.Object,
	 * java.lang.Object, java.lang.Object)
	 */
	@Override
	public T replace(T original, T target, Object owner) throws HibernateException {
		return original; // PersistentEnum is immutable
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#assemble(java.io.Serializable,
	 * java.lang.Object)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public T assemble(Serializable cached, Object owner) throws HibernateException {
		return (T) cached; // PersistentEnum is immutable
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#disassemble(java.lang.Object)
	 */
	@Override
	public Serializable disassemble(T value) throws HibernateException {
		return (Serializable) value;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#equals(java.lang.Object,
	 * java.lang.Object)
	 */
	@Override
	public boolean equals(T x, T y) throws HibernateException {
		return x == y;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#hashCode(java.lang.Object)
	 */
	@Override
	public int hashCode(T x) throws HibernateException {
		return x == null ? 0 : x.hashCode();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#nullSafeGet(java.sql.ResultSet, int,
	 * org.hibernate.engine.spi.SharedSessionContractImplementor, java.lang.Object)
	 */
	@Override
	public T nullSafeGet(ResultSet rs, int position, SharedSessionContractImplementor session, Object owner)
			throws SQLException {

		if (rs.wasNull()) {
			return null;
		}

		J id = getValue(rs, position);

		for (T value : returnedClass().getEnumConstants()) {
			if (id instanceof String) {

				if (((String) id).equalsIgnoreCase((String) value.getId())) {
					return value;
				}

			} else {
				if (id == value.getId()) {
					return value;
				}
			}
		}

		throw new IllegalStateException("Unknown " + returnedClass().getSimpleName() + " id");
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#nullSafeSet(java.sql.PreparedStatement,
	 * java.lang.Object, int,
	 * org.hibernate.engine.spi.SharedSessionContractImplementor)
	 */
	@Override
	public void nullSafeSet(PreparedStatement st, T value, int index, SharedSessionContractImplementor session)
			throws SQLException {
		if (value == null) {
			st.setNull(index, getSqlType());
		} else {
			setParam(st, (T) value, index);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.hibernate.usertype.UserType#returnedClass()
	 */
	@Override
	public abstract Class<T> returnedClass();

	/**
	 * @param rs
	 * @param position
	 * @return
	 * @throws SQLException
	 */
	public abstract J getValue(ResultSet rs, int position) throws SQLException;

	/**
	 * @return
	 */
	public abstract void setParam(PreparedStatement st, T value, int index) throws SQLException;

	/**
	 * @return
	 */
	public abstract int getSqlType();

}