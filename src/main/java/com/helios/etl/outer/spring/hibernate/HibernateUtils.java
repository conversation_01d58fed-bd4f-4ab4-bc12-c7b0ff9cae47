/**
 * 
 */
package com.helios.etl.outer.spring.hibernate;

import org.hibernate.Hibernate;
import org.hibernate.proxy.HibernateProxy;

/**
 * <AUTHOR>
 *
 */
public class HibernateUtils {

	/**
	 * @param entity
	 * @return
	 */
	public static <T> T initializeAndUnproxy(T entity) {
		if (entity == null) {
			throw new NullPointerException("Entity passed for initialization is null");
		}

		Hibernate.initialize(entity);
		if (entity instanceof HibernateProxy) {
			entity = (T) ((HibernateProxy) entity).getHibernateLazyInitializer().getImplementation();
		}
		return entity;
	}

}
