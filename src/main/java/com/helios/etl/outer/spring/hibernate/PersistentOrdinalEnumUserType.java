/**
 * 
 */
package com.helios.etl.outer.spring.hibernate;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

/**
 * 
 *
 */
public abstract class PersistentOrdinalEnumUserType<R extends PersistentOrdinalEnum>
		extends PersistentEnumUserType<Integer, R> {

	@Override
	public Integer getValue(ResultSet rs, int position) throws SQLException {
		return rs.getInt(position);
	}

	@Override
	public void setParam(PreparedStatement st, R value, int index) throws SQLException {
		st.setInt(index, value.getId());
	}

	@Override
	public int getSqlType() {
		return Types.SMALLINT;
	}

}