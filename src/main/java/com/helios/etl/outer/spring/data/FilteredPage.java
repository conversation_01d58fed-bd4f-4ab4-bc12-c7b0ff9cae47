/**
 * 
 */
package com.helios.etl.outer.spring.data;

import java.util.List;

import com.helios.etl.outer.spring.jackson.FilterView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import com.fasterxml.jackson.annotation.JsonView;


/**
 * Permet d'afficher une Page quand une FilterView est appliquée
 * <AUTHOR>
 *
 */
public class FilteredPage<T> {

	/**
	 * <AUTHOR>
	 *
	 */
	public class FilteredSort {
		
		private Sort sort;

		public FilteredSort(Sort sort) {
			super();
			this.sort = sort;
		}

		@JsonView(FilterView.Public.class)
		public boolean isEmpty() {
			return sort.isEmpty();
		}

		@JsonView(FilterView.Public.class)
		public boolean isSorted() {
			return sort.isSorted();
		}

		@JsonView(FilterView.Public.class)
		public boolean isUnsorted() {
			return sort.isUnsorted();
		}
		
		
	}
	
	/**
	 * <AUTHOR>
	 *
	 */
	public class FilteredPageable{
		
		private Pageable pageable;

		public FilteredPageable(Pageable pageable) {
			super();
			this.pageable = pageable;
		}

		@JsonView(FilterView.Public.class)
		public boolean isPaged() {
			return pageable.isPaged();
		}

		@JsonView(FilterView.Public.class)
		public boolean isUnpaged() {
			return pageable.isUnpaged();
		}

		@JsonView(FilterView.Public.class)
		public int getPageNumber() {
			return pageable.getPageNumber();
		}

		@JsonView(FilterView.Public.class)
		public int getPageSize() {
			return pageable.getPageSize();
		}

		@JsonView(FilterView.Public.class)
		public long getOffset() {
			return pageable.getOffset();
		}

		@JsonView(FilterView.Public.class)
		public FilteredSort getSort() {
			return new FilteredSort(pageable.getSort());
		}
		
	}
	
	/**
	 * 
	 */
	protected Page<T> page;
	
	/**
	 * @param page
	 */
	public FilteredPage(Page<T> page) {
		this.page = page;
	}
	
	/**
	 * @param content
	 * @param pageable
	 * @param total
	 */
	public FilteredPage(List<T> content, Pageable pageable, long total) {
		this.page = new PageImpl<T>(content, pageable, total);
	}
	
	/**
	 * @param content
	 */
	public FilteredPage(List<T> content) {
		this.page = new PageImpl<T>(content);
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public int getNumber() {
		return page.getNumber();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public int getSize() {
		return page.getSize();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public int getTotalPages() {
		return page.getTotalPages();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public int getNumberOfElements() {
		return page.getNumberOfElements();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public long getTotalElements() {
		return page.getTotalElements();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public List<T> getContent() {
		return page.getContent();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public FilteredSort getSort() {
		return new FilteredSort(page.getSort());
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public boolean isFirst() {
		return page.isFirst();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public boolean isLast() {
		return page.isLast();
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public FilteredPageable getPageable() {
		return new FilteredPageable(page.getPageable());
	}

	/**
	 * @return
	 */
	@JsonView(FilterView.Public.class)
	public boolean isEmpty() {
		return page.isEmpty();
	}
	
}
