/**
 * 
 */
package com.helios.etl.outer.spring.hibernate;


import com.helios.etl.model.TypeRelationIssue;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

/**
 * 
 *
 */
public abstract class PersistentLiteralEnumUserType<R extends PersistentLiteralEnum> extends PersistentEnumUserType<String, R> {


    @Override
    public String getValue(ResultSet rs, int position) throws SQLException {
        return rs.getString(position);
    }

    @Override
    public void setParam(PreparedStatement st, R value, int index) throws SQLException {
        st.setString(index, value.getId());
    }

    @Override
    public int getSqlType() {
        return Types.VARCHAR;
    }

}