/**
 * 
 */
package com.helios.etl.outer.spring.jpa;

import com.helios.etl.model.TypeRelationIssue;
import com.helios.etl.outer.spring.hibernate.PersistentLiteralEnumUserType;

/**
 * <AUTHOR>
 *
 */
public class TypeRelationIssueUserType extends PersistentLiteralEnumUserType<TypeRelationIssue> {

    /* (non-Javadoc)
     * @see fr.actuelburo.spring.hibernate.PersistentEnumUserType#returnedClass()
     */
    @Override
    public Class<TypeRelationIssue> returnedClass() {
        return TypeRelationIssue.class;
    }
    
}
