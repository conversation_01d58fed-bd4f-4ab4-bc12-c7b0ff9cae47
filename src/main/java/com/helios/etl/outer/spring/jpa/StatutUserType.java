/**
 * 
 */
package com.helios.etl.outer.spring.jpa;

import com.helios.etl.model.Statut;
import com.helios.etl.outer.spring.hibernate.PersistentLiteralEnumUserType;

/**
 * <AUTHOR>
 *
 */
public class StatutUserType extends PersistentLiteralEnumUserType<Statut> {

    /* (non-Javadoc)
     * @see fr.actuelburo.spring.hibernate.PersistentEnumUserType#returnedClass()
     */
    @Override
    public Class<Statut> returnedClass() {
        return Statut.class;
    }
    
}
