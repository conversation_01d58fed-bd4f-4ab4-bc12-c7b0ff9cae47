/**
 * 
 */
package com.helios.etl.outer.model;

import java.time.LocalDateTime;
import java.util.Map;

import com.helios.etl.outer.DateTimeConstants;
import com.helios.etl.outer.spring.jackson.FilterView;
import com.helios.etl.outer.utils.beans.BeanUpdateIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import org.hibernate.annotations.Type;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;

import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "log_requests",
	indexes = {
		@Index(name = "index_startDate", 			columnList = "startDate", unique = false),
		@Index(name = "index_endDate", 	 			columnList = "endDate",   unique = false),
		@Index(name = "index_principal", 			columnList = "principal", unique = false),
		@Index(name = "index_sessionId", 			columnList = "sessionId", unique = false),
		@Index(name = "index_serverApplication", 	columnList = "serverApplication", unique = false)
	}
)
public class TransactionLog {

	public static final String PRINCIPAL_ANONYMOUS = "anonymous";
	
	/**
	 * 
	 */
	@Id
    @Getter
    @Setter
    @Column(columnDefinition = "CHAR(36)")
    protected String uid;

	/**
	 * 
	 */
	@Getter
	@Setter
	@Version
	@JsonIgnore
    @BeanUpdateIgnore
	protected Integer __version;
	
	/**
	 * 
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime startDate = LocalDateTime.now();
	
	/**
	 * 
	 */
	@Column(nullable = false)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime endDate = LocalDateTime.now();

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "bigint default 0 ")
	@Getter
	@Setter
	protected long tickStart = 0;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "bigint default 0 ")
	@Getter
	@Setter
	protected long tickEnd = 0;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "bigint default 0 ")
	@Getter
	@Setter
	protected long duration = 0;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(10) default 'GET' ")
	@Getter
	@Setter
	protected String method = "GET";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(255) default '' ")
	@Getter
	@Setter
	protected String path = "";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(15) default '' ")
	@Getter
	@Setter
	protected String scheme = "";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(150) default '' ")
	@Getter
	@Setter
	protected String principal = PRINCIPAL_ANONYMOUS;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(50) default '' ")
	@Getter
	@Setter
	protected String sessionId = "";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(255) default '' ")
	@Getter
	@Setter
	protected String serverName = "";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "integer default 80 ")
	@Getter
	@Setter
	protected int serverPort = 80;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(50) default '' ")
	@Getter
	@Setter
	protected String serverApplication = "";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(50) default '' ")
	@Getter
	@Setter
	protected String remoteAddress = "";

	/**
	 * 
	 */
    @Type(JsonStringType.class)
    @Column(columnDefinition = "json")
	@Getter
	@Setter
	protected Map<String, String> requestHeaders;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(255) default '' ")
	@Getter
	@Setter
	protected String requestContentType = "";
    
	/**
	 * 
	 */
    @Type(JsonStringType.class)
    @Column(columnDefinition = "json")
	@Getter
	@Setter
	protected Map<String, String> requestParameters;
	
	/**
	 * 
	 */
	@Column(columnDefinition = "longtext")
	@Getter
	@Setter 
	protected String requestBody = "";

	/**
	 * 
	 */
    @Type(JsonStringType.class )
    @Column(columnDefinition = "json")
	@Getter
	@Setter
	protected Map<String, String> responseHeaders;

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "varchar(255) default '' ")
	@Getter
	@Setter
	protected String responseContentType = "";

	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "integer(4) default 0 ")
	@Getter
	@Setter
	protected int responseStatus = 0;
	
	/**
	 * 
	 */
	@Column(columnDefinition = "longtext")
	@Getter
	@Setter 
	@JsonIgnore
	protected String responseBody = "";
	
	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "bit(1) default 0")
	@Getter
	@Setter
	protected boolean ended = false;
	
	/**
	 * 
	 */
	@Column(nullable = false, columnDefinition = "bit(1) default 0")
	@Getter
	@Setter
	protected boolean exception = false;
	
	/**
	 * 
	 */
	@Column(nullable = true, columnDefinition = "text")
	@Getter
	@Setter
	@JsonView(FilterView.Public.class)
	protected String exceptionType = "";
	
	/**
	 * 
	 */
	@Column(nullable = true, columnDefinition = "longtext")
	@Getter
	@Setter
	@JsonView(FilterView.Public.class)
	protected String exceptionMessage = "";
    
	/**
	 * 
	 */
	public TransactionLog() {

//        uid = TransactionLogsService.nextUid();
	}
	
	/**
	 * 
	 */
	public void resolved() {
		setEnded(true);
		setEndDate(LocalDateTime.now());
		setTickEnd(System.currentTimeMillis());
		setDuration(getTickEnd() - getTickStart());
	}
	
	/**
	 * 
	 */
	public void unresolved() {
		setEnded(false);
		setEndDate(LocalDateTime.now());
		setTickEnd(System.currentTimeMillis());
		setDuration(getTickEnd() - getTickStart());
	}

}
