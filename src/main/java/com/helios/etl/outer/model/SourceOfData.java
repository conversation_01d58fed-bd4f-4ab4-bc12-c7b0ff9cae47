/**
 * 
 */
package com.helios.etl.outer.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.helios.etl.outer.DateTimeConstants;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 */
@Embeddable 
@Access(AccessType.FIELD)
public class SourceOfData implements Serializable {

	private static final long serialVersionUID = 2354638930571601442L;

	/**
	 * Oid de la source de données
	 */
	@Column(name = "src_oid", columnDefinition = "bigint unsigned default 0", nullable = true)
	@Getter
	@Setter
	protected Long oid = null;
	
	/**
	 * Type java de la source de données
	 */
	@Column(name = "src_kind", length = 255, nullable = true)
	@Getter
	@Setter
	protected String kind = null;
	
	/**
	 * Nom de la source de données
	 */
	@Column(name = "src_name", length = 255, nullable = true)
	@Getter
	@Setter
	protected String name = null;
	
	/**
	 * Date de mise à jour de la source
	 */
	@Column(name = "src_date", nullable = true)
	@DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
	@Getter
	@Setter
	protected LocalDateTime dateUpdated = null;

}
