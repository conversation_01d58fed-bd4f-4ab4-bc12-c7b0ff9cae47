/**
 * 
 */
package com.helios.etl.outer.model;

import java.util.Map;

import org.hibernate.annotations.Type;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 */
@MappedSuperclass
public class ExternalEntity {
	
	/**
	 * Identifiant unique dans la base de données
	 */
	@Id
	@Column(unique = true, nullable = false)
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Getter
	@Setter
	protected long oid;

	/**
	 * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
	 */
	@Getter
	@Setter
	@Version
	@JsonIgnore
	protected Integer __version;
	
	/**
	 * Cet entité est associé à une source de données externe
	 */
	@Column(nullable = false, columnDefinition = "bit(1) default 0")
	@Getter
	@Setter
	protected boolean externe = false;

	/**
	 * Source externe de données de la personne
	 */
	@Embedded
	@Getter
	@Setter
	protected SourceOfData externalSource;
	
	/**
	 * Données diverses
	 */
	@Type(JsonStringType.class)
	@Column(columnDefinition = "json", nullable = true)
	@Getter
	@Setter
	protected Map<String, String> data = null;	

}
