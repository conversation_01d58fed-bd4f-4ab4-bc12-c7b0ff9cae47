/**
 * 
 */
package com.helios.etl.outer.utils.templates;

/**
 * <AUTHOR>
 *
 */
public abstract class ExportStrategyHelper implements ExportStrategy {

	/**
	 * 
	 */
	protected String extension = "bin";
	
	/**
	 * 
	 */
	protected String mimeType = "application/binary";
	
	/**
	 * 
	 */
	protected String contentTransfer = "binary";
	
	/**
	 * 
	 */
	protected boolean htmlContent = false;
	
	/**
	 * 
	 */
	protected boolean forceDownload = false;

	/**
	 * 
	 */
	public ExportStrategyHelper() {
		
	}
	
	/**
	 * @param extension
	 * @param mimeType
	 * @param contentTransfer
	 * @param forceDownload
	 */
	public ExportStrategyHelper(String extension, String mimeType, String contentTransfer, boolean forceDownload) {
		this.extension = extension;
		this.mimeType = mimeType;
		this.contentTransfer = contentTransfer;
	}
	
	/**
	 * @param extension
	 * @param mimeType
	 * @param forceDownload
	 */
	public ExportStrategyHelper(String extension, String mimeType, boolean forceDownload) {
		this.extension = extension;
		this.mimeType = mimeType;
	}
	
	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.templates.ExportStrategy#mimeType()
	 */
	@Override
	public String mimeType() {
		return mimeType;
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.templates.ExportStrategy#fileExtension()
	 */
	@Override
	public String fileExtension() {
		return extension;
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.templates.ExportStrategy#contentTransfer()
	 */
	@Override
	public String contentTransfer() {
		return contentTransfer;
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.templates.ExportStrategy#forceDownload()
	 */
	@Override
	public boolean forceDownload() {
		return forceDownload;
	}

}


