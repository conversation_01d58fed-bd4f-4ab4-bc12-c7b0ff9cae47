/**
 * 
 */
package com.helios.etl.outer.utils.poi;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.regex.Matcher;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import com.helios.etl.outer.utils.FindReplaceMapper;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class DocxFindReplace {
	
	/**
	 * 
	 */
	@Getter
	protected boolean dirty;
	
	/**
	 * 
	 */
	protected XWPFDocument document;
	
	/**
	 * 
	 */
	@Getter
	@Setter
	protected List<ContentHandler> handlers = new ArrayList<>();

	/**
	 * @param pathname
	 * @throws IOException
	 * @throws InvalidFormatException
	 */
	public DocxFindReplace(String pathname) throws IOException, InvalidFormatException {
		from(new XWPFDocument(OPCPackage.open(pathname)));
	}
	
	/**
	 * @param document
	 * @throws IOException
	 * @throws InvalidFormatException
	 */
	public DocxFindReplace(XWPFDocument document) throws IOException, InvalidFormatException {
		from(document);
	}
	
	/**
	 * @param file
	 * @throws IOException
	 * @throws InvalidFormatException
	 */
	public DocxFindReplace(File file) throws IOException, InvalidFormatException {
		from(new XWPFDocument(OPCPackage.open(file)));
	}

	/**
	 * @param pathname
	 * @throws IOException
	 * @throws InvalidFormatException
	 */
	public void from(String pathname) throws IOException, InvalidFormatException {
		from(new XWPFDocument(OPCPackage.open(pathname)));
	}
	
	/**
	 * @param file
	 * @throws IOException
	 * @throws InvalidFormatException
	 */
	public void from(File file) throws IOException, InvalidFormatException {
		from(new XWPFDocument(OPCPackage.open(file)));
	}
	
	/**
	 * @param document
	 * @throws IOException
	 * @throws InvalidFormatException
	 */
	public void from(XWPFDocument document) throws IOException, InvalidFormatException {
		if(this.document != null) {
			try {
				this.document.getPackage().revert();
			} catch (Exception e) {
			}
		}
		
		this.document = document;
		
		if(handlers.size() == 0) {
			handlers.add(new StringsHandler());
//			handlers.add(new BarcodesHandler());
		}
	}
	
	/**
	 * @param mapper
	 */
	public void replace(FindReplaceMapper mapper) {
		
		for(ContentHandler handler : handlers) {
			iterateParagraphs(document, p -> searchTags(p, mapper, handler));
		}

	}

	/**
	 * @return
	 */
	public XWPFDocument export() {
		return document;
	}
	
	/**
	 * @return
	 * @throws IOException 
	 */
	public void cleanup() throws IOException {
		document.close();
	}
	
	/**
	 * @param doc
	 * @param mapper
	 * @param consumer
	 */
	protected void iterateParagraphs(XWPFDocument doc, Consumer<XWPFParagraph> consumer) {
		
		for (XWPFParagraph p : doc.getParagraphs()) {
			consumer.accept(p);
		}
		
		for (XWPFTable tbl : doc.getTables()) {
			for (XWPFTableRow row : tbl.getRows()) {
				for (XWPFTableCell cell : row.getTableCells()) {
					for (XWPFParagraph p : cell.getParagraphs()) {
						consumer.accept(p);
					}
				}
			}
		}
	}
	
	/**
	 * @param paragraph
	 * @param mapper
	 */
	protected void searchTags(XWPFParagraph paragraph, FindReplaceMapper mapper, ContentHandler handler) {

		String prefix1 = handler.getPrefix().substring(0, 1);
		String prefix2 = handler.getPrefix().substring(1);

		if(!paragraph.getText().contains(handler.getPrefix())) {
			return;
		}
		
		String partialTag = "";
		
		List<XWPFRun> runs = paragraph.getRuns();
		for (int i = 0; i < runs.size(); i++) {
			
			XWPFRun run = runs.get(i);
			String text = run.getText(0);
			
			if(partialTag.length() > 0) {
				text = partialTag + text;
				partialTag = "";
			}
			
			if(text == null) {
				continue;
			}
			
			if (text.contains(handler.getPrefix()) ||
					(text.contains(prefix1) && runs.get(i + 1).getText(0).substring(0, 1).equals(prefix2))) {
				
				while (!text.contains(handler.getSuffix())) {
					XWPFRun nextRun = runs.get(i + 1);
					text = text + nextRun.getText(0);
					paragraph.removeRun(i + 1);
				}
				
				// Un prefix est prÃ©sent mais son suffixe est plus loin, on doit le retirer
				// pour le traiter plus tard
				int lastIndexPrefix = text.lastIndexOf(prefix1);
				if(lastIndexPrefix > text.lastIndexOf(handler.getSuffix())) {
					partialTag = text.substring(lastIndexPrefix);
					text =  text.substring(0, lastIndexPrefix);
				}
				
				Matcher m = handler.getPattern().matcher(text);

				while (m.find()) {
					
					String key = m.group(1);
					String needle = handler.getPrefix() + key + handler.getSuffix();
					
					text = handler.replace(run, text, key, needle, mapper);
					m.reset(text);
					
				}
				
			}
			
		}
	}
	
}


