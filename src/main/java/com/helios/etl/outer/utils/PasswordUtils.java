/**
 * 
 */
package com.helios.etl.outer.utils;

import java.util.ArrayList;
import java.util.List;

import com.helios.etl.outer.utils.restClient.LogicException;
import org.passay.CharacterData;
import org.passay.CharacterRule;
import org.passay.EnglishCharacterData;
import org.passay.LengthRule;
import org.passay.PasswordData;
import org.passay.PasswordGenerator;
import org.passay.PasswordValidator;
import org.passay.Rule;
import org.passay.RuleResult;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class PasswordUtils {

	public static final int DEFAULT_LENGTH = 10;

	public static final String DEFAULT_DEFINITION = "1,1,1,1,10";
	
	/**
	 * <AUTHOR>
	 *
	 */
	public static class ValidationResult {
		
		/**
		 * @return
		 */
		@Getter
		@Setter
		private boolean valid;
		
		@Getter
		private List<String> messages;
		
		@Getter
		private double entropy;

		/**
		 * @param valid
		 * @param messages
		 */
		public ValidationResult(boolean valid, List<String> messages, double entropy) {
			super();
			this.valid = valid;
			this.messages = messages;
			this.entropy = entropy;
		}

	}
	
	/**
	 * @param password
	 * @return
	 */
	public static ValidationResult validatePassword(String password) {
		return validatePassword(password, defaultRules());
	}
	
	/**
	 * @param password
	 * @return
	 */
	public static ValidationResult validateSecuredPassword(String password) {
		return validatePassword(password, securedRules());
	}
	
	/**
	 * @param password
	 * @return
	 */
	public static ValidationResult validateEnforcedPassword(String password) {
		return validatePassword(password, securedRules());
	}
	
	/**
	 * @param password
	 * @param rules
	 * @return
	 */
	public static ValidationResult validatePassword(String password, Rule[] rules) {
		PasswordValidator validator = new PasswordValidator(rules);
		PasswordData data = new PasswordData(password);
		RuleResult result = validator.validate(data);
		return new ValidationResult(result.isValid(), validator.getMessages(result), validator.estimateEntropy(data));
	}
	
	/**
	 * @param password
	 * @param format
	 * @return
	 */
	public static ValidationResult validatePassword(String password, String format) throws LogicException {
		return validatePassword(password, toValidationRules(format));
	}
	
	/**
	 * @return
	 */
	public static String generatePassword() {
		return generatePassword(DEFAULT_LENGTH);
	}
	
	/**
	 * @param length
	 * @return
	 */
	public static String generatePassword(int length) {
		return generatePassword(length, defaultCharacterRules());
	}
	
	/**
	 * @param format
	 * @return
	 */
	public static String generatePassword(String format) throws LogicException {
		int length = PasswordUtils.extractLength(format);
		String definition = PasswordUtils.removeLength(format);
		return generatePassword(length, toGenerationRules(definition));
	}
	
	/**
	 * @param length
	 * @param rules
	 * @return
	 */
	public static String generatePassword(int length, CharacterRule[] rules) {
		String password = new PasswordGenerator().generatePassword(length, rules);
		return password;
	}
	
	/**
	 * @return
	 */
	public static CharacterRule[] defaultCharacterRules() {
		return characterRules(1, 1, 1, 0);
	}
	
	/**
	 * @return
	 */
	public static Rule[] defaultRules() {
		return rules(1, 1, 1, 0, 10);
	}
	
	/**
	 * @return
	 */
	public static Rule[] securedRules() {
		return rules(1, 1, 1, 1, 10);
	}
	
	/**
	 * @return
	 */
	public static Rule[] enforcedRules() {
		return rules(2, 2, 2, 2, 20);
	}

	/**
	 * @param definition
	 * @param nbParts
	 * @return
	 */
	public static int[] extractDefinitionParts(String definition, int nbParts) throws LogicException {
		
		if(definition == null) {
			throw new LogicException("PasswordUtils", "definition cannot be null");
		}
		
		String[] defs = definition.split("\\s*,\\s*");
		
		if(defs.length < nbParts) {
			throw new LogicException("PasswordUtils", "wrong definition : " + definition);
		}
		
		int[] parts = new int[nbParts];
		for(int i = 0; i < nbParts; i++) {
			parts[i] = Integer.parseInt(defs[i]);
		}
		
		return parts;
	}
	
	/**
	 * @param definition
	 * @return
	 */
	public static int extractLength(String definition) throws LogicException {
		
		int[] parts = extractDefinitionParts(definition, 5);
		return parts[4];
	}
	
	/**
	 * @param definition
	 * @return
	 */
	public static String removeLength(String definition) throws LogicException {
		
		int[] parts = extractDefinitionParts(definition, 5);
		return new StringBuilder()
			.append(parts[0]).append(",")
			.append(parts[1]).append(",")
			.append(parts[2]).append(",")
			.append(parts[3])
			.toString();
	}
	
	/**
	 * @param definition
	 * @return
	 */
	public static Rule[] toValidationRules(String definition) throws LogicException {
		
		int[] parts = extractDefinitionParts(definition, 5);
		
		int lc = parts[0];
		int uc = parts[1];
		int digit = parts[2];
		int spl = parts[3];
		int len = parts[4];
		
		return rules(lc, uc, digit, spl, len);
	}
	
	/**
	 * @param definition
	 * @return
	 */
	public static CharacterRule[] toGenerationRules(String definition) throws LogicException {
		
		int[] parts = extractDefinitionParts(definition, 4);
		
		int lc = parts[0];
		int uc = parts[1];
		int digit = parts[2];
		int spl = parts[3];
		
		return characterRules(lc, uc, digit, spl);
	}
	
	/**
	 * @param lc
	 * @param uc
	 * @param digit
	 * @param spl
	 * @return
	 */
	public static CharacterRule[] characterRules(int lc, int uc, int digit, int spl) {
		
		List<CharacterRule> rules = new ArrayList<>();
		
		if(lc > 0) {
			CharacterRule lowerCaseRule = new CharacterRule(EnglishCharacterData.LowerCase);
			lowerCaseRule.setNumberOfCharacters(lc);
			rules.add(lowerCaseRule);
		}

		if(uc > 0) {
			CharacterRule upperCaseRule = new CharacterRule(EnglishCharacterData.UpperCase);
			upperCaseRule.setNumberOfCharacters(uc);
			rules.add(upperCaseRule);
		}

		if(digit > 0) {
			CharacterRule digitRule = new CharacterRule(EnglishCharacterData.Digit);
			digitRule.setNumberOfCharacters(digit);
			rules.add(digitRule);
		}

		if(spl > 0) {
			CharacterRule splCharRule = new CharacterRule(new CharacterData() {
				
				@Override
				public String getCharacters() {
					return "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~";
				}

				@Override
				public String getErrorCode() {
					return "INSUFFICIENT_SPECIAL";
				}
			});
			splCharRule.setNumberOfCharacters(spl);
			rules.add(splCharRule);
		}

		return rules.toArray(new CharacterRule[] {});
		
	}
	
	/**
	 * @param lc
	 * @param uc
	 * @param digit
	 * @param spl
	 * @param len
	 * @return
	 */
	public static Rule[] rules(int lc, int uc, int digit, int spl, int len) {
		
		List<Rule> rules = new ArrayList<>();
		
		for(Rule r : characterRules(lc, uc, digit, spl)) {
			rules.add(r);
		}

		if(len > 0) {
			LengthRule rule = new LengthRule();
			rule.setMinimumLength(len);
			rules.add(rule);
		}

		return rules.toArray(new Rule[] {});
		
	}

}


