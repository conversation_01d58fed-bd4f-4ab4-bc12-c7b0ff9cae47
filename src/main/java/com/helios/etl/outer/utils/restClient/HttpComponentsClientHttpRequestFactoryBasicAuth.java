/**
 *
 */
package com.helios.etl.outer.utils.restClient;

import java.net.URI;

import org.apache.hc.client5.http.auth.AuthCache;
import org.apache.hc.client5.http.impl.auth.BasicAuthCache;
import org.apache.hc.client5.http.impl.auth.BasicScheme;
import org.apache.hc.client5.http.protocol.HttpClientContext;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.http.protocol.BasicHttpContext;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;




/**
 * <AUTHOR>
 *
 */
public class HttpComponentsClientHttpRequestFactoryBasicAuth extends HttpComponentsClientHttpRequestFactory {

    /**
     * 
     */
    private HttpHost host;

    /**
     * @param host
     */
    public HttpComponentsClientHttpRequestFactoryBasicAuth(HttpHost host) {
        super();
        this.host = host;
    }

    /* (non-Javadoc)
     * @see org.springframework.http.client.HttpComponentsClientHttpRequestFactory#createHttpContext(org.springframework.http.HttpMethod, java.net.URI)
     */
    @Override
    protected HttpContext createHttpContext(HttpMethod httpMethod, URI uri) {
        AuthCache cache = new BasicAuthCache();
        cache.put(host, new BasicScheme());

        BasicHttpContext context = new BasicHttpContext();
        context.setAttribute(HttpClientContext.AUTH_CACHE, cache);
        
        return context;
    }

}

