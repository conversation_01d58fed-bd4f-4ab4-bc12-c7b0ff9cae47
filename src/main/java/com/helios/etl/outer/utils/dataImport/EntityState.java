package com.helios.etl.outer.utils.dataImport;

/**
 * <AUTHOR>
 *
 */
public enum EntityState {
	
	/**
	 * 
	 */
	ADDED("added"), 
	
	/**
	 * 
	 */
	UPDATED("updated"), 
	
	/**
	 * 
	 */
	DELETED("deleted"),
	
	/**
	 * 
	 */
	UNCHANGED("unchanged");
	
	/**
	 * 
	 */
	@SuppressWarnings("unused")
	private String state;
	
	/**
	 * @param state
	 */
	private EntityState(String state) {
		this.state = state;
	}
	
}

