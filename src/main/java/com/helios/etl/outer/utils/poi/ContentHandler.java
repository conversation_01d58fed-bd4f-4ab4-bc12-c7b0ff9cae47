package com.helios.etl.outer.utils.poi;

import java.util.regex.Pattern;

import org.apache.poi.xwpf.usermodel.XWPFRun;

import com.helios.etl.outer.utils.FindReplaceMapper;

/**
 * <AUTHOR>
 *
 */
public interface ContentHandler  {
	  
	/**
	 * @return
	 */
	public String getPrefix();
	  
	/**
	 * @return
	 */
	public String getSuffix();
	  
	/**
	 * @return
	 */
	public Pattern getPattern();
	
	/**
	 * @param run
	 * @param text
	 * @param key
	 * @param neddle
	 * @param mapper
	 */
	public String replace(XWPFRun run, String text, String key, String neddle, FindReplaceMapper mapper);
	
}


