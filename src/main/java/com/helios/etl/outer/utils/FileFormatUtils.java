package com.helios.etl.outer.utils;
/**
 * 
 */

import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.util.stream.Stream;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

/**
 * <AUTHOR>
 *
 */
public class FileFormatUtils {

	/**
	 *
	 * @param <T>
	 */
	public interface CSVRecordConverter<T>{
		
		/**
		 * @param source
		 * @return
		 */
		public Iterable<?> convert(T source);
		
	}
	
	/**
	 *
	 */
	public interface CSVRecordable {
		
		/**
		 * @param source
		 * @return
		 */
		public Iterable<?> toCSVRecord();
		
	}
	
	/**
	 * @param csvfile
	 * @return
	 * @throws IOException
	 */
	public static CSVPrinter createCSVPrinterForExcel(File csvfile) throws IOException {
		
		BufferedWriter writer = Files.newBufferedWriter(csvfile.toPath(), StandardOpenOption.WRITE);
		
		// Microsoft software tends to assume windows-12* or UTF-16LE charsets, unless the content starts with a byte order mark 
		// which the software will use to identify the charset. 
		// Try adding a byte order mark at the start of the file
		// https://stackoverflow.com/questions/57112252/generate-csv-via-apache-csv-in-utf-8
		// https://stackoverflow.com/questions/42715966/preserve-utf-8-bom-in-browser-downloads
		writer.write('\uFEFF');
		writer.write('\uFEFF');
		
		CSVFormat format = CSVFormat.EXCEL
								.withAllowMissingColumnNames()
								.withFirstRecordAsHeader()
								.withDelimiter(';')
								.withEscape('\\')
								.withIgnoreEmptyLines()
								.withQuote('"');

		CSVPrinter printer = new CSVPrinter(writer, format);
		
		return printer;
	}
	
	/**
	 * @param csvfile
	 * @param values
	 * @return
	 * @throws IOException 
	 */
	public static File createCsvForExcel(File csvfile, final Iterable<?> values) throws IOException {

		CSVPrinter printer = createCSVPrinterForExcel(csvfile);
		printer.printRecords(values);
		printer.close();

		return csvfile;
		
	}

	/**
	 * @param csvfile
	 * @param values
	 * @return
	 * @throws IOException 
	 */
	public static File createCsvForExcel(File csvfile, final String[]headers,  final Stream<?> stream) throws IOException {


		CSVPrinter printer = createCSVPrinterForExcel(csvfile);
		
		if(headers != null) {
			printer.printRecord((Object[]) headers);
		}
		
		stream.forEach(values -> {
			try {
				printer.printRecords(values);
			} catch (IOException e) {
				e.printStackTrace();
			}
		});

		printer.close();

		return csvfile;
		
	}

	/**
	 * @param csvfile
	 * @param headers
	 * @param values
	 * @param converter
	 * @return
	 * @throws IOException
	 */
	public static <T> File createCsvForExcel(File csvfile, final String[]headers,  final Iterable<T> values, CSVRecordConverter<T> converter) throws IOException {


		CSVPrinter printer = createCSVPrinterForExcel(csvfile);
		
		if(headers != null) {
			printer.printRecord((Object[]) headers);
		}
		
		for(T value : values){
			try {
				printer.printRecord(converter.convert(value));
			} catch (IOException e) {
				e.printStackTrace();
			}
		};

		printer.close();

		return csvfile;
		
	}

	/**
	 * @param csvfile
	 * @param headers
	 * @param values
	 * @param converter
	 * @return
	 * @throws IOException
	 */
	public static File createCsvForExcel(File csvfile, final String[]headers,  final Iterable<? extends CSVRecordable> values) throws IOException {


		CSVPrinter printer = createCSVPrinterForExcel(csvfile);
		
		if(headers != null) {
			printer.printRecord((Object[]) headers);
		}
		
		for(CSVRecordable value : values){
			try {
				printer.printRecord(value.toCSVRecord());
			} catch (IOException e) {
				e.printStackTrace();
			}
		};

		printer.close();

		return csvfile;
		
	}
}


