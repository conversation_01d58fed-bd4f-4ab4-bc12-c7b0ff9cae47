/**
 * 
 */
package com.helios.etl.outer.utils;

import com.helios.etl.outer.DateTimeConstants;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 *
 */
public interface FindReplaceMapper {

	/**
	 * @param key
	 * @return value to replace or null to ignore
	 */
	public String map(String key);

	/**
	 * @param key
	 */
	public boolean hasMap(String key);

	/**
	 * @param time
	 * @param defaultValue
	 * @return
	 */
	default public String formatTime(LocalTime time, String defaultValue) {
		if(time == null) {
			return defaultValue;
		} else {
			return time.format(DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_TIME));
		}
	}
	
	/**
	 * @param time
	 * @return
	 */
	default public String formatTime(LocalTime time) {
		return formatTime(time, null);
	}
	
	/**
	 * @param date
	 * @param defaultValue
	 * @return
	 */
	default public String formatDate(LocalDate date, String defaultValue) {
		if(date == null) {
			return defaultValue;
		} else {
			return date.format(DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_DATE));
		}
	}
	
	/**
	 * @param date
	 * @return
	 */
	default public String formatDate(LocalDate date) {
		return formatDate(date, null);
	}
	
	/**
	 * @param date
	 * @param defaultValue
	 * @return
	 */
	default public String formatDate(LocalDateTime datetime, String defaultValue) {
		if(datetime == null) {
			return null;
		} else {
			return datetime.format(DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_DATE));
		}
	}
	
	/**
	 * @param datetime
	 * @return
	 */
	default public String formatDate(LocalDateTime datetime) {
		return formatDate(datetime, null);
	}
	
	/**
	 * @param date
	 * @param defaultValue
	 * @return
	 */
	default public String formatDateTime(LocalDateTime datetime, String defaultValue) {
		if(datetime == null) {
			return null;
		} else {
			return datetime.format(DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_DATETIME));
		}
	}
	
	/**
	 * @param datetime
	 * @return
	 */
	default public String formatDateTime(LocalDateTime datetime) {
		return formatDate(datetime, null);
	}
	
	/**
	 * 
	 * @param date
	 * @param defaultValue
	 * @param format
	 * @return
	 */
	default public String customFormatDate(LocalDate date, String defaultValue, String format) {
		
		if(date == null) {
			return defaultValue;
		} else {
			
			if (format == null) {
				return formatDate(date);
			}
			
			DateTimeFormatter formatter;
			
			try {
				formatter = DateTimeFormatter.ofPattern(format);				
			} catch (IllegalArgumentException e ) {
				return formatDate(date)  + " (format incorrect)";
			}
			
			return formatter.format(date);
		}
		
	}
	
	/**
	 * 
	 * @param date
	 * @param format
	 * @return
	 */
	default public String customFormatDate(LocalDate date, String format) {
		return customFormatDate(date, null, format);
	}
	
	/**
	 * 
	 * @param date
	 * @param defaultValue
	 * @param format
	 * @return
	 */
	default public String customFormatDate(LocalDateTime datetime, String defaultValue, String format) {
		if(datetime == null) {
			return defaultValue;
		} else {
			
			if (format == null) {
				return formatDate(datetime);
			}
			
			DateTimeFormatter formatter;
			try {
				formatter = DateTimeFormatter.ofPattern(format);				
			} catch (IllegalArgumentException e) {
				return formatDate(datetime) + " (format incorrect)";
			}
			return formatter.format(datetime);
		}
		
	}
	
	/**
	 * 
	 * @param datetime
	 * @param format
	 * @return
	 */
	default public String customFormatDate(LocalDateTime datetime,  String format) {
		return customFormatDate(datetime, null, format);
		
	}
	
	/**
	 * 
	 * @param time
	 * @param defaultValue
	 * @param format
	 * @return
	 */
	default public String customFormatTime(LocalTime time, String defaultValue, String format) {
		if(time == null) {
			return defaultValue;
		} else {
			
			if (format == null) {
				return formatTime(time);
			}
			
			DateTimeFormatter formatter;
			try {
				formatter = DateTimeFormatter.ofPattern(format);				
			} catch (IllegalArgumentException e) {
				return formatTime(time) + " (format incorrect)";
			}
			return formatter.format(time);
		}
		
	}
	
	/**
	 * 
	 * @param time
	 * @param format
	 * @return
	 */
	default public String customFormatTime(LocalTime time, String format) {
		return customFormatTime(time, null, format);
		
	}
	
	/**
	 * 
	 * @param tag
	 * @return
	 */
	default String extractDateFormatFromTag(String tag) {
		String[] split = tag.split("[:]");
		return split[1];
		
	}
	
	/**
	 * 
	 * @param tag
	 * @return
	 */
	default String extractKeyFromTag(String tag) {
		String[] split = tag.split("[:]");
		return split[0];
		
	}
}


