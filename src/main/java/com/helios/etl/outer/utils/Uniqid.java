/**
 * 
 */
package com.helios.etl.outer.utils;

import java.nio.ByteBuffer;
import java.util.Random;

import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 *
 */
public class Uniqid {

	/**
	 * 
	 */
	private Uniqid() {

	}
	
	/**
	 * Gets a unique identifier based on the current time in microseconds
	 * 
	 * @return
	 */
	static public String next() {
		return next(false);
	}
	
	/**
	 * Gets a unique identifier based on the current time in microseconds
	 * 
	 * @param moreEntropy This method does not guarantee uniqueness of return value. Since most systems adjust system clock by NTP or like, 
	 * system time is changed constantly. Therefore, it is possible that this function does not return unique ID for 
	 * @return
	 */
	static public String next(boolean moreEntropy) {
		return next("", moreEntropy);
	}
	
	/**
	 * Gets a prefixed unique identifier based on the current time in microseconds
	 * 
	 * @param prefix
	 * @return
	 */
	static public String next(String prefix) {
		return next(prefix, false);
	}
	
	/***
	 * Gets a prefixed unique identifier based on the current time in microseconds
	 * 
	 * This method does not generate cryptographically secure values, and should not be used for cryptographic purposes.
	 * 
	 * @see http://php.net/manual/fr/function.uniqid.php
	 * @param prefix
	 * @param moreEntropy This method does not guarantee uniqueness of return value. Since most systems adjust system clock by NTP or like, 
	 * system time is changed constantly. Therefore, it is possible that this function does not return unique ID for 
	 * the process/thread. Use moreEntropy to increase likelihood of uniqueness
	 * @return
	 */
	static public String next(String prefix, boolean moreEntropy) {
		
		long time = System.currentTimeMillis();

		String uniqid = String.format("%s%08x%05x", prefix, time / 1000, time);
		
		if(moreEntropy) {
			byte[] bytes = new byte[8];
			new Random().nextBytes(bytes);
			uniqid += "." + String.format("%.8s", "" + ByteBuffer.wrap(bytes).getLong() * -1);
		}

		return uniqid;
	}
	
	/**
	 * Gets a prefixed unique identifier based on the current time in microseconds
	 * 
	 * @param prefix
	 * @return
	 */
	static public String nextMd5() {
		return nextMd5("", false);
	}
	
	/**
	 * Gets a prefixed unique identifier based on the current time in microseconds
	 * 
	 * @param prefix
	 * @return
	 */
	static public String nextMd5(String prefix) {
		return nextMd5(prefix, false);
	}
	
	/**
	 * Gets a prefixed unique identifier based on the current time in microseconds
	 * 
	 * @param prefix
	 * @return
	 */
	static public String nextMd5(boolean moreEntropy) {
		return nextMd5("", moreEntropy);
	}
	
	/**
	 * @param prefix
	 * @param moreEntropy
	 * @return
	 */
	static public String nextMd5(String prefix, boolean moreEntropy) {
		return prefix + DigestUtils.md5DigestAsHex(next("", moreEntropy).getBytes());
	}

}


