/**
 * 
 */
package com.helios.etl.outer.utils;

/**
 * <AUTHOR>
 *
 */
public class DebugUtils {

	private static final int DEFAULT_COUNTDOWN = 1;

	private static final int PREVIOUS_COUNTDOWN = 2;
	
	/**
	 * @return The StackTraceElement of the code that ran this method
	 * <AUTHOR>
	 */
	public static StackTraceElement currentStackTraceElement() {
		return ___8drrd3148796d_Xaf(DEFAULT_COUNTDOWN);
	}
	
	/**
	 * @return The StackTraceElement of the code that ran this method
	 * <AUTHOR>
	 */
	public static StackTraceElement previousStackTraceElement() {
		return ___8drrd3148796d_Xaf(PREVIOUS_COUNTDOWN);
	}

	/**
	 * This methods name is ridiculous on purpose to prevent any other method names
	 * in the stack trace from potentially matching this one.
	 * 
	 * @param countdown
	 * @return The StackTraceElement of the code that called the method that called this method.
	 * <AUTHOR>
	 */
	private static StackTraceElement ___8drrd3148796d_Xaf(int countdown) {
		
		boolean thisOne = false;
		StackTraceElement[] elements = Thread.currentThread().getStackTrace();
		
		for (StackTraceElement element : elements) {
			
			String methodName = element.getMethodName();
			
			if (thisOne && countdown == 0) {
				return element;
				
			} else if (thisOne) {
				countdown--;
			}
			
			if (methodName.equals("___8drrd3148796d_Xaf")) {
				thisOne = true;
			}
		}
		
		return null;
	}

}


