/**
 * 
 */
package com.helios.etl.outer.utils.remote;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class RemoteDock {
	
	/**
	 * 
	 */
	public static final String PROTOCOL_FTP = "ftp";
	
	/**
	 * 
	 */
	public static final String PROTOCOL_FTPS = "ftps";
	
	/**
	 * 
	 */
	public static final String PROTOCOL_SFTP = "sftp";

	/**
	 * 
	 */
	@Getter
	@Setter
	protected String protocol;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected String endpoint;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected int port;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected String username;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected String password;
			
	/**
	 * 
	 */
	public RemoteDock() {

	}

	public RemoteOperations operations() throws RemoteDockException {
		
		switch(protocol) {
		
			case PROTOCOL_FTP:
				return new FTPRemoteOperations(this);
				
			case PROTOCOL_FTPS:
			case PROTOCOL_SFTP:
			default:
				throw new RemoteDockException("Unsupported protocol " + protocol);
		}
	}
}


