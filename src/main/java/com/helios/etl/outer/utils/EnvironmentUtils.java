/**
 * 
 */
package com.helios.etl.outer.utils;

import java.util.HashMap;
import java.util.Map;

import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;

/**
 * <AUTHOR>
 *
 */
public class EnvironmentUtils {
	
	/**
	 * @param env
	 */
	public static Map<String, String> filterProperties(Environment env, String filter) {
		Map<String, String> properties = new HashMap<String, String>();
		
		if (env instanceof ConfigurableEnvironment) {
			
			for (PropertySource<?> propertySource : ((ConfigurableEnvironment)env).getPropertySources()) {
				
				if (propertySource instanceof EnumerablePropertySource) {
					for (String propertyKey : ((EnumerablePropertySource<?>) propertySource).getPropertyNames()) {
						
						if (propertyKey.startsWith(filter)) {
							properties.put(propertyKey, env.getProperty(propertyKey));
						}
						
					}
				}
				
			}
			
		}
		
		return properties;
		
	}
	
}


