/**
 * 
 */
package com.helios.etl.outer.utils.regex;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public class SimpleDottedConverter implements PatternMatcherConverter {

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.regex.PatternMatcherConverter#supports(java.lang.String)
	 */
	@Override
	public boolean supports(String rule) {
		return StringUtils.containsAny(rule, '*', '?');
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.regex.PatternMatcherConverter#compile(java.lang.String)
	 */
	@Override
	public PatternMatcher compile(String rule) {
		String formatted = String.format(
			"^%s$", 
			rule.replace(".", "\\.").replace(".?", ".[^.]+").replace("*", ".*")
		);

		return new RegexPatternMatcher(formatted);
	}

}


