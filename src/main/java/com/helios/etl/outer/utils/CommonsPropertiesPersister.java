/**
 * 
 */
package com.helios.etl.outer.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.util.Arrays;
import java.util.Properties;

import org.apache.commons.configuration2.ConfigurationConverter;
import org.apache.commons.configuration2.PropertiesConfiguration;
import org.apache.commons.configuration2.ex.ConfigurationException;
import org.apache.commons.configuration2.io.ClasspathLocationStrategy;
import org.apache.commons.configuration2.io.CombinedLocationStrategy;
import org.apache.commons.configuration2.io.FileLocationStrategy;
import org.apache.commons.configuration2.io.FileLocator;
import org.apache.commons.configuration2.io.FileLocatorUtils;
import org.apache.commons.configuration2.io.FileSystemLocationStrategy;
import org.apache.commons.configuration2.io.ProvidedURLLocationStrategy;
import org.springframework.core.io.Resource;
import org.springframework.util.PropertiesPersister;

/**
 * Implementation of the {@link PropertiesPersister} interface based on {@code org.apache.commons.configuration.PropertiesConfiguration}
 * <AUTHOR>
 */
public class CommonsPropertiesPersister implements PropertiesPersister {
	
	/**
	 * 
	 */
	private PropertiesConfiguration shared;
	
	/**
	 * 
	 */
	private FileLocator locator;
	
	/**
	 * @param sharedFile
	 * @param basepath
	 * @param charset
	 */
	public CommonsPropertiesPersister(File sharedFile, File basepath, String charset) {
		
		FileLocationStrategy strategy = new CombinedLocationStrategy(Arrays.asList(
			new ProvidedURLLocationStrategy(),
			new FileSystemLocationStrategy(), 
			new ClasspathLocationStrategy()	
		));
			
		locator = FileLocatorUtils.fileLocator()
				.locationStrategy(strategy)
				.basePath(basepath.getAbsolutePath())
				.encoding(charset).create();
			
		try {
			shared = read(new InputStreamReader(new FileInputStream(sharedFile)));
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		
	}

	/**
	 * @param sharedFile
	 * @param basepath
	 * @param charset
	 * @throws IOException 
	 */
	public CommonsPropertiesPersister(Resource sharedFile, Resource basepath, String charset) throws IOException {
		
		FileLocationStrategy strategy = new CombinedLocationStrategy(Arrays.asList(
			new ProvidedURLLocationStrategy(),
			new FileSystemLocationStrategy(), 
			new ClasspathLocationStrategy()	
		));

		locator = FileLocatorUtils.fileLocator()
				.locationStrategy(strategy)
				//.basePath(basepath.getFile().getAbsolutePath())
				.basePath(basepath.getURL().toString())
				.encoding(charset).create();
		
		try {
			shared = read(new InputStreamReader(sharedFile.getInputStream()));
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		
	}
	
	/**
	 * @param file
	 * @return
	 */
	public Properties load(File file){
		Properties props = new Properties();
		
		try {
			load(props, new FileInputStream(file));
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		
		return props;
	}
	
	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#load(java.util.Properties, java.io.InputStream)
	 */
	@Override
	public void load(Properties props, InputStream is) throws IOException {
		load(props, new InputStreamReader(is));
	}

	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#load(java.util.Properties, java.io.Reader)
	 */
	@Override
	public void load(Properties props, Reader reader) throws IOException {
		
		PropertiesConfiguration configuration = read(reader);
		configuration.append(shared);
		props.putAll(ConfigurationConverter.getMap(configuration.interpolatedConfiguration()));

	}

	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#store(java.util.Properties, java.io.OutputStream, java.lang.String)
	 */
	@Override
	public void store(Properties props, OutputStream os, String header) throws IOException {
		props.store(os, header);
	}

	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#store(java.util.Properties, java.io.Writer, java.lang.String)
	 */
	@Override
	public void store(Properties props, Writer writer, String header) throws IOException {
		props.store(writer, header);
	}

	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#loadFromXml(java.util.Properties, java.io.InputStream)
	 */
	@Override
	public void loadFromXml(Properties props, InputStream is) throws IOException {
		throw new IOException("Unimplemented method");
	}

	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#storeToXml(java.util.Properties, java.io.OutputStream, java.lang.String)
	 */
	@Override
	public void storeToXml(Properties props, OutputStream os, String header) throws IOException {
		throw new IOException("Unimplemented method");
	}

	/* (non-Javadoc)
	 * @see org.springframework.util.PropertiesPersister#storeToXml(java.util.Properties, java.io.OutputStream, java.lang.String, java.lang.String)
	 */
	@Override
	public void storeToXml(Properties props, OutputStream os, String header, String encoding) throws IOException {
		throw new IOException("Unimplemented method");
	}

	/**
	 * @param reader
	 * @return
	 * @throws IOException
	 */
	protected PropertiesConfiguration read(Reader reader) throws IOException {
		
		PropertiesConfiguration configuration = new PropertiesConfiguration();
		configuration.initFileLocator(locator);
	
		try {
			configuration.read(reader);
		} catch (ConfigurationException e) {
			throw new IOException(e);
		}
		
		return configuration;
		
	}

}


