/**
 * 
 */
package com.helios.etl.outer.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.Normalizer;
import java.text.Normalizer.Form;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import fr.opensagres.xdocreport.core.utils.Base64Utility;

/**
 * <AUTHOR>
 *
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

	/**
	 * 
	 */
	public static final String HTML_EMBED_IMG_SRC_PREFIX = "data:;base64,";

	/**
	 * 
	 */
	private static final String TAG_START = "@{";

	/**
	 * 
	 */
	private static final String TAG_END = "}";
	/**
	 * 
	 */
	private static final Pattern NONLATIN = Pattern.compile("[^\\w-]");
	/**
	 * 
	 */
	private static final Pattern WHITESPACE = Pattern.compile("[\\s]");

	/**
	 * 
	 */
	private static final Pattern PHONE_PATTERN = Pattern.compile("(\\d{10})");

	/**
	 * 
	 */
	private static final Pattern INTERNATIONAL_PHONE_PATTERN = Pattern.compile("(\\+|00)(\\d{11})");

	/**
	 *  
	 */
	private static final Pattern EMAIL_PATTERN = Pattern.compile("^\\S+@\\S+\\.\\S+$");

	/**
	 * @param value
	 * @return
	 */
	public static String urlEncode(String value) {
		try {
			return URLEncoder.encode(value, StandardCharsets.ISO_8859_1.toString());
		} catch (UnsupportedEncodingException e) {
			return value.replace("#", "%23");
		}
	}

	/**
	 * @param bytes
	 * @return
	 */
	public static String convertToDataBase64Embedded(byte[] bytes) {
		StringBuilder sb = new StringBuilder(bytes.length + HTML_EMBED_IMG_SRC_PREFIX.length())
				.append(HTML_EMBED_IMG_SRC_PREFIX).append(Base64Utility.encode(bytes));
		return sb.toString();
	}

	/**
	 * @param content
	 * @param values
	 * @return
	 */
	public static String findReplace(String content, Map<String, String> values) {

		return findReplace(content, new FindReplaceMapper() {

			@Override
			public String map(String key) {
				if (values.containsKey(key)) {
					return values.get(key);
				} else {
					return null;
				}
			}

			@Override
			public boolean hasMap(String key) {
				return values.containsKey(key);
			}
		});

	}

	/**
	 * @param content
	 * @param values
	 * @return
	 */
	public static String findReplace(String content, FindReplaceMapper mapper) {

		String quoted = Pattern.quote(TAG_START) + "([^" + TAG_END + "]+)" + Pattern.quote(TAG_END);
		Pattern p = Pattern.compile(quoted);

		Matcher m = p.matcher(content);

		while (m.find()) {

			String key = m.group(1);
			String value = mapper.map(key);

			if (value != null) {
				content = content.replace(m.group(0), value);
				m.reset(content);
			}

		}

		return content;
	}

	/**
	 * @param data
	 * @return
	 */
	public static String convertToHex(byte[] data) {
		StringBuffer buf = new StringBuffer();

		for (int i = 0; i < data.length; i++) {
			int halfbyte = (data[i] >>> 4) & 0x0F;
			int two_halfs = 0;

			do {

				if ((0 <= halfbyte) && (halfbyte <= 9)) {
					buf.append((char) ('0' + halfbyte));
				} else {
					buf.append((char) ('a' + (halfbyte - 10)));
				}

				halfbyte = data[i] & 0x0F;

			} while (two_halfs++ < 1);
		}

		return buf.toString();

	}

	/**
	 * @param s
	 * @return
	 */
	public static byte[] hexStringToByteArray(String s) {
		int len = s.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
		}
		return data;
	}

	/**
	 * @param valeur
	 * @param taille
	 * @param caractere
	 * @return
	 */
	public static String completeStringLeft(String valeur, int taille, char caractere) {
		return completeString(valeur, taille, caractere, false);
	}

	/**
	 * @param valeur
	 * @param taille
	 * @param caractere
	 * @return
	 */
	public static String completeStringRight(String valeur, int taille, char caractere) {
		return completeString(valeur, taille, caractere, true);
	}

	/**
	 * @param valeur
	 * @param taille
	 * @param caractere
	 * @param sens
	 * @return
	 */
	private static String completeString(String valeur, int taille, char caractere, boolean sens) {

		String completion = "";
		String resultat = "";

		if (valeur.length() < taille) {

			completion = org.apache.commons.lang3.StringUtils.repeat(caractere, taille - valeur.length());

			// completion Ã  droite
			if (sens) {
				resultat = String.format("%s%s", valeur, completion);
			}
			// completion Ã  gauche
			else {
				resultat = String.format("%s%s", completion, valeur);
			}

			return resultat;

		} else {
			resultat = valeur.substring(0, taille);
		}

		return resultat;

	}

	/**
	 * @param value
	 * @return
	 */
	public static String ucfirst(String value) {

		if (value == null || value.length() == 0) {
			return value;
		} else if (value.length() == 1) {
			return value.toUpperCase();
		} else {
			return value.substring(0, 1).toUpperCase() + value.substring(1).toLowerCase();
		}

	}

	/**
	 * 
	 * @param x
	 * @param y
	 * @return
	 */
	public static int levensteinDistance(String x, String y) {
		int[][] dp = new int[x.length() + 1][y.length() + 1];

		for (int i = 0; i <= x.length(); i++) {
			for (int j = 0; j <= y.length(); j++) {
				if (i == 0) {
					dp[i][j] = j;
				} else if (j == 0) {
					dp[i][j] = i;
				} else {
					dp[i][j] = min(dp[i - 1][j - 1] + costOfSubstitution(x.charAt(i - 1), y.charAt(j - 1)),
							dp[i - 1][j] + 1, dp[i][j - 1] + 1);
				}
			}
		}

		return dp[x.length()][y.length()];
	}

	/**
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	protected final static int costOfSubstitution(char a, char b) {
		return a == b ? 0 : 1;
	}

	/**
	 * 
	 * @param numbers
	 * @return
	 */
	protected final static int min(int... numbers) {
		return Arrays.stream(numbers).min().orElse(Integer.MAX_VALUE);
	}

	/**
	 * @param input
	 * @return
	 */
	public static String makeSlug(String input) {
		if (input == null) {
			throw new IllegalArgumentException();
		}
		String nowhitespace = WHITESPACE.matcher(input).replaceAll("-");
		String normalized = Normalizer.normalize(nowhitespace, Form.NFD);
		String slug = NONLATIN.matcher(normalized).replaceAll("");
		return slug.toLowerCase(Locale.ENGLISH);
	}

	/**
	 * @param input
	 * @return
	 */
	public static boolean isPhoneNumber(String input) {
		input = deleteSpaces(input);
		return isMobilePhoneNumber(input) || isInternationalPhoneNumber(input);

	}

	/**
	 * @param input
	 * @return
	 */
	public static boolean isMobilePhoneNumber(String input) {
		input = deleteSpaces(input);
		Matcher matcher = PHONE_PATTERN.matcher(input);
		return (matcher.matches());

	}

	/**
	 * @param input
	 * @return
	 */
	public static boolean isInternationalPhoneNumber(String input) {
		input = deleteSpaces(input);
		Matcher matcher = INTERNATIONAL_PHONE_PATTERN.matcher(input);
		return (matcher.matches());

	}

	/**
	 * @param input
	 * @return
	 */
	public static String phoneNumberFormat(String originalInput) {
		String input = deleteSpaces(originalInput);
		if (!isPhoneNumber(input)) {
			return originalInput;
		}

		String regex = "";
		String formatString = "";
		if (isMobilePhoneNumber(input)) {
			
			//regex = "(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})";			
			//formatString = "$1 $2 $3 $4 $5";
			//regex = "(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})";
			//formatString = "$1 $2 $3 $4 $5 $6 $7 $8 $9 $10";
			regex = "(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})(\\d{1})";
			formatString = "$1 $2 $3 $4 $5 $6 $7 $8 $9 $10";
			
			
		} else {
			if (isInternationalPhoneNumber(input)) {
				regex = "(\\+|00)(\\d{2})(\\d{1})(\\d{2})(\\d{2})(\\d{2})(\\d{2})";

				if (input.contains("+")) {
					formatString = "$1$2 $3 $4 $5 $6 $7";

				} else {
					formatString = "$1 $2 $3 $4 $5 $6 $7";
				}

			}
		}
		
		//String formatedString = " (\""+input.replaceFirst(regex, formatString)+"\")";
		//return formatedString +" (\""+input+"\")";
		
		String formatedString = input.replaceFirst(regex, formatString) ;
		formatedString = formatedString.replace(" " , "%");
		return formatedString;
	}

	/**
	 * @param input
	 * @return
	 */
	public static String deleteSpaces(String input) {
		return input.replace(" ","");
	}

	/**
	 * @param input
	 * @return
	 */
	public static boolean containsEmail(String input) {

		String[] rawTerms = input.split(" ");

		for (int i = 0; i < rawTerms.length; i++) {

			if (rawTerms[i].length() <= 1 && rawTerms.length > 1) {
				continue;
			}
			if (isEmail(rawTerms[i])) {
				return true;
			}

		}
		return false;
	}

	/**
	 * @param input
	 * @return
	 */
	public static boolean isEmail(String input) {
		if (input.contains("@")) {

			Matcher matcher = EMAIL_PATTERN.matcher(input);
			return (matcher.matches());
		}
		return false;
	}

	/**
	 * @param input
	 * @return
	 */
	public static String replaceEmailWithStringEmail(String input) {
		if (containsEmail(input)) {
			String regex = "(\\S+@\\S+\\.\\S+)";
			return (input.replaceAll(regex,"\"$1\""));
		}
		return input;
	}

	/**
	 * @param input
	 * @return
	 */
	public static String replaceEmailWithWhiteSpace(String input) {
		if (containsEmail(input)) {
			String regex = "(\\S+@\\S+\\.\\S+)";
			return (input.replaceAll(regex," "));
		}
		return input;
	}
	
	/**
	 * @param input
	 * @return
	 */
	public static List<String> retrieveEmails(String input) {
		List<String> occurences = new ArrayList<String>(); 
		if (containsEmail(input)) {
			String regex = "(\\S+@\\S+\\.\\S+)";
			Matcher matcher = Pattern.compile(regex).matcher(input);
			while (matcher.find()) {
			    String s = matcher.group(1);
			    occurences.add(s);
			}
			
		}
		return occurences;
	}

	/**
	 * @param query
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	public static Map<String, List<String>> splitHttpQueryParameters(String query) throws UnsupportedEncodingException{
		Map<String, List<String>> fields = new LinkedHashMap<>();
		String[] pairs = query.split("&");
		
	    for (String pair : pairs) {
	        final int idx = pair.indexOf("=");
	        final String key = idx > 0 ? URLDecoder.decode(pair.substring(0, idx), "UTF-8") : pair;
	        
	        if (!fields.containsKey(key)) {
	        	fields.put(key, new LinkedList<String>());
	        }
	        
	        final String value = idx > 0 && pair.length() > idx + 1 ? URLDecoder.decode(pair.substring(idx + 1), "UTF-8") : null;
	        fields.get(key).add(value);
	    }
	    
		return fields;
	}

	
	/**
	 * EnlÃ¨ve la partie du nom de classe remontÃ© par le proxy.
	 * @param className
	 * @return
	 */
	public static String removeProxyFromClassName(String className) {
		String newClassName = className;
		
		if (newClassName.contains("$")) {
			newClassName = newClassName.substring(0, newClassName.indexOf("$"));
		}
		
		return newClassName;
	}
	
	/**
	 * @param input
	 * @return
	 */
	public static String replaceSpecialChars(String input) {
        if (input == null) {
            return null;
        }

        // Remplace les caractÃ¨res spÃ©ciaux franÃ§ais par leurs Ã©quivalents non accentuÃ©s
        String replacedString = input
                .replaceAll("[Ã©Ã¨ÃªÃ«]", "e")
                .replaceAll("[Ã‰ÃˆÃŠÃ‹]", "E")
                .replaceAll("[Ã Ã¢Ã¤]", "a")
                .replaceAll("[Ã€Ã‚Ã„]", "A")
                .replaceAll("[Ã´Ã¶]", "o")
                .replaceAll("[Ã”Ã–]", "O")
                .replaceAll("[Ã¹Ã»Ã¼]", "u")
                .replaceAll("[Ã™Ã›Ãœ]", "U")
                .replaceAll("Ã§", "c")
                .replaceAll("Ã‡", "C");

        return replacedString;
    }
	
	/**
	 * @param input
	 * @return
	 */
	public static Map<String, String> convertStringToMap(String input) {
		
		if(input == null || input.length()<2) {
			return null;
		}
		
		Map<String, String> reconstructedUtilMap = Arrays.stream(input.substring(1, input.length() - 1).split(","))
				.map(s -> s.split("=", 2)).collect(Collectors.toMap(s -> s[0].trim(), s -> s[1].trim()));

		return reconstructedUtilMap;
	}
	
	/**
	 * @param input
	 * @return
	 */
	public static String convertMapToString(Map<String,String> input) {
		
		if(input == null) {
			return null;
		}
		
		String join =StringUtils.join(input,",");
		if(join.endsWith(",")) {
			join = join.substring(0, join.length() - 1);
		}
		 
		return join;
	}

}


