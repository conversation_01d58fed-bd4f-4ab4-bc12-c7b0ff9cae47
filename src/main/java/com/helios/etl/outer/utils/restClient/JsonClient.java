/**
 * 
 */
package com.helios.etl.outer.utils.restClient;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.Charset;

import org.apache.hc.core5.http.HttpHost;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public class JsonClient {
	
	/**
	 * 
	 */
	@Getter
	private RestTemplate client;
	
	/**
	 * @param url
	 * @param user
	 * @param password
	 * @throws URISyntaxException 
	 */
	public JsonClient(String url, String username, String password) throws LogicException {
		
		HttpHost host = null;
		try {
			HttpHost.create(url);
		} catch (URISyntaxException e) {
			throw new LogicException("JsonClient", e.getMessage());
		}
		
		client = new RestTemplate(new BufferingClientHttpRequestFactory(new HttpComponentsClientHttpRequestFactoryBasicAuth(host)));
		client.getInterceptors().add(new BasicAuthenticationInterceptor(username, password));
		client.getInterceptors().add(new LoggingRequestInterceptor());
		client.getMessageConverters().add(0, new StringHttpMessageConverter(Charset.forName("UTF-8")));
	}
	
	/**
	 * @param url 
	 */
	public JsonClient(String url) throws LogicException {
		
		HttpHost host = null;
		try {
			HttpHost.create(url);
		} catch (URISyntaxException e) {
			throw new LogicException("JsonClient", e.getMessage());
		}
		
		client = new RestTemplate(new BufferingClientHttpRequestFactory(new HttpComponentsClientHttpRequestFactoryBasicAuth(host)));
		client.getInterceptors().add(new LoggingRequestInterceptor());
		client.getMessageConverters().add(0, new StringHttpMessageConverter(Charset.forName("UTF-8")));
	}
	
	/**
	 * @param builder
	 * @return
	 * @throws IOException
	 */
	public JsonNode get(UriComponentsBuilder builder) throws IOException {
		return call(builder, HttpMethod.GET);
	}
	
	/**
	 * @param builder
	 * @param headers
	 * @return
	 * @throws IOException
	 */
	public JsonNode get(UriComponentsBuilder builder, HttpHeaders headers) throws IOException {
		return call(builder, HttpMethod.GET, headers);
	}
	
	/**
	 * @param builder
	 * @return
	 * @throws IOException
	 */
	public JsonNode post(UriComponentsBuilder builder) throws IOException {
		return call(builder, HttpMethod.POST);
	}
	
	/**
	 * @param builder
	 * @param headers
	 * @return
	 * @throws IOException
	 */
	public JsonNode post(UriComponentsBuilder builder, Object body, HttpHeaders headers) throws IOException {
		return call(builder, HttpMethod.POST, body, headers);
	}
	
	/**
	 * @param builder
	 * @param method
	 * @return
	 * @throws IOException
	 */
	public JsonNode call(UriComponentsBuilder builder, HttpMethod method) throws IOException {
		
		HttpHeaders headers = new HttpHeaders();
		headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
		
		HttpEntity<?> requestEntity = new HttpEntity<Object>(headers);
		
		ResponseEntity<String> response = client.exchange(builder.build().toUri(), method, requestEntity, String.class);

		ObjectMapper mapper = new ObjectMapper();
		if(response.getBody() != null) {
			return mapper.readTree(response.getBody());
		} else {
			return mapper.readTree("{}");
		}
		
	}
	
	/**
	 * @param builder
	 * @param method
	 * @param headers
	 * @param parseResponse
	 * @return
	 * @throws IOException
	 */
	public JsonNode call(UriComponentsBuilder builder, HttpMethod method, HttpHeaders headers) throws IOException {

		HttpEntity<?> requestEntity = new HttpEntity<Object>(headers);
		
		ResponseEntity<String> response = client.exchange(builder.build().toUri(), method, requestEntity, String.class);

		ObjectMapper mapper = new ObjectMapper();
		if(response.getBody() != null) {
			return mapper.readTree(response.getBody());
		} else {
			return mapper.readTree("{}");
		}
	
		
	}
	
	/**
	 * @param builder
	 * @param method
	 * @return
	 * @throws IOException
	 */
	public JsonNode call(UriComponentsBuilder builder, HttpMethod method, Object body, HttpHeaders headers) throws IOException {

		HttpEntity<?> requestEntity = new HttpEntity<Object>(body, headers);

		ResponseEntity<String> response = client.exchange(builder.build().toUri(), method, requestEntity, String.class);

		ObjectMapper mapper = new ObjectMapper();
		if(response.getBody() != null) {
			return mapper.readTree(response.getBody());
		} else {
			return mapper.readTree("{}");
		}
		
	}
	
}


