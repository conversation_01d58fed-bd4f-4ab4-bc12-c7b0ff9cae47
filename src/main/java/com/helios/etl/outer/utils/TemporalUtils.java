/**
 * 
 */
package com.helios.etl.outer.utils;

import com.helios.etl.outer.DateTimeConstants;

import static java.time.temporal.TemporalAdjusters.firstDayOfMonth;
import static java.time.temporal.TemporalAdjusters.lastDayOfMonth;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Date;

import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <AUTHOR>
 *
 */
final public class TemporalUtils {

	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_ISO_DATE = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_ISO_DATE);
	
	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_ISO_TIME = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_ISO_TIME);
	
	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_ISO_DATETIME = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_ISO_DATETIME);

	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_DATE = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_DATE);
	
	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_TIME = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_TIME);
	
	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_DATETIME = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_DATETIME);
	
	/**
	 * 
	 */
	public static final DateTimeFormatter FORMAT_DATE_PATH = DateTimeFormatter.ofPattern(DateTimeConstants.FORMAT_DATE_PATH);
	
	/**
	 * 
	 */
	private TemporalUtils() {
	}

	/**
	 * @return
	 */
	public static LocalDate unixEpochDate() {
		return LocalDate.of(1970, 1, 1);
	}

	/**
	 * @return
	 */
	public static LocalDateTime unixEpochDateTime() {
		return LocalDateTime.of(1970, 1, 1, 0, 0, 0);
	}

	/**
	 * @param date
	 * @return
	 */
	public static LocalDate orUnixEpochDate(LocalDate date) {
		return date == null ? unixEpochDate() : date;
	}

	/**
	 * @param datetime
	 * @return
	 */
	public static LocalDateTime orUnixEpochDateTime(LocalDateTime datetime) {
		return datetime == null ? unixEpochDateTime() : datetime;
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isFirstDayOfWeek(LocalDate t1) {
		return t1.getDayOfWeek() == DayOfWeek.MONDAY;
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isFirstDayOfWeek(LocalDateTime t1) {
		return t1.getDayOfWeek() == DayOfWeek.MONDAY;
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isLastDayOfWeek(LocalDate t1) {
		return t1.getDayOfWeek() == DayOfWeek.SUNDAY;
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isLastDayOfWeek(LocalDateTime t1) {
		return t1.getDayOfWeek() == DayOfWeek.SUNDAY;
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isFirstDayOfMonth(LocalDate t1) {
		return t1.getDayOfMonth() == 1;
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isFirstDayOfMonth(LocalDateTime t1) {
		return t1.getDayOfMonth() == 1;
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isLastDayOfMonth(LocalDate t1) {
		return t1.getDayOfMonth() == t1.lengthOfMonth();
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isLastDayOfMonth(LocalDateTime t1) {
		return t1.getDayOfMonth() == t1.toLocalDate().lengthOfMonth();
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isSameDay(LocalDate t1, LocalDate t2) {
		return isEquals(t1, t2);
	}
	
	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isSameDay(LocalDateTime t1, LocalDateTime t2) {
		return isEquals(t1.toLocalDate(), t2.toLocalDate());
	}

	/**
	 * @param t1
	 * @return
	 */
	public static boolean isToday(LocalDate t1) {
		return isEquals(t1, LocalDate.now());
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isToday(LocalDateTime t1) {
		return isEquals(t1.toLocalDate(), LocalDate.now());
	}
	
	/**
	 * @param time
	 * @return
	 */
	public static LocalTime aquaoLastMinute(LocalTime time) {
		
		String minute = Integer.toString(time.getMinute());
		
		if(minute.endsWith("0") || minute.endsWith("5")) {
			return time.minusMinutes(1);
		} else {
			return time;
		}
	}
	
	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBefore(LocalTime t1, LocalTime t2) {
		return t1.isBefore(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBefore(LocalDate t1, LocalDate t2) {
		return t1.isBefore(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBefore(LocalDateTime t1, LocalDateTime t2) {
		return t1.isBefore(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isAfter(LocalTime t1, LocalTime t2) {
		return t1.isAfter(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isAfter(LocalDate t1, LocalDate t2) {
		return t1.isAfter(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isAfter(LocalDateTime t1, LocalDateTime t2) {
		return t1.isAfter(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBeforeOrEquals(LocalTime t1, LocalTime t2) {
		return t1.equals(t2) || t1.isBefore(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBeforeOrEquals(LocalDate t1, LocalDate t2) {
		return t1.equals(t2) || t1.isBefore(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBeforeOrEquals(LocalDateTime t1, LocalDateTime t2) {
		return t1.equals(t2) || t1.isBefore(t2);
	}
	
	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isAfterOrEquals(LocalTime t1, LocalTime t2) {
		return t1.equals(t2) || t1.isAfter(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isAfterOrEquals(LocalDate t1, LocalDate t2) {
		return t1.isAfter(t2) || t1.equals(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isAfterOrEquals(LocalDateTime t1, LocalDateTime t2) {
		return t1.isAfter(t2) || t1.equals(t2);
	}
	
	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isEquals(LocalTime t1, LocalTime t2) {
		return t1.equals(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isEquals(LocalDate t1, LocalDate t2) {
		return  t1.equals(t2);
	}

	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isEquals(LocalDateTime t1, LocalDateTime t2) {
		return  t1.equals(t2);
	}
	
	/**
	 * @param date
	 * @return
	 */
	public static String format(LocalDate date) {
		return date.format(FORMAT_DATE);
	}
	
	/**
	 * @param time
	 * @return
	 */
	public static String format(LocalTime time) {
		return time.format(FORMAT_TIME);
	}

	/**
	 * @param time
	 * @return
	 */
	public static String format(LocalDateTime time) {
		return time.format(FORMAT_DATETIME);
	}

	/**
	 * @param date
	 * @return
	 */
	public static String formatIso(LocalDate date) {
		return date.format(FORMAT_ISO_DATE);
	}
	
	/**
	 * @param time
	 * @return
	 */
	public static String formatIso(LocalTime time) {
		return time.format(FORMAT_ISO_TIME);
	}

	/**
	 * @param time
	 * @return
	 */
	public static String formatIso(LocalDateTime time) {
		return time.format(FORMAT_ISO_DATETIME);
	}

	/**
	 * Compares its two arguments for order. Returns a negative integer, zero, or 
	 * a positive integer as the first argument is less than, equal to, or greater than the second.
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static int compareAsc(LocalDate t1, LocalDate t2) {
		if(t1.equals(t2)){
			return 0;
		} else if(t1.isBefore(t2)) {
			return -1;
		} else {
			return 1;
		}
	}
	
	/**
	 * Compares its two arguments for order. Returns a negative integer, zero, or 
	 * a positive integer as the first argument is less than, equal to, or greater than the second.
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static int compareAsc(LocalTime t1, LocalTime t2) {
		if(t1.equals(t2)){
			return 0;
		} else if(t1.isBefore(t2)) {
			return -1;
		} else {
			return 1;
		}
	}

	/**
	 * Compares its two arguments for order. Returns a negative integer, zero, or 
	 * a positive integer as the first argument is less than, equal to, or greater than the second.
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static int compareAsc(LocalDateTime t1, LocalDateTime t2) {
		if(t1.equals(t2)){
			return 0;
		} else if(t1.isBefore(t2)) {
			return -1;
		} else {
			return 1;
		}
	}
	
	/**
	 * Compares its two arguments for order. Returns a negative integer, zero, or 
	 * a positive integer as the first argument is less than, equal to, or greater than the second.
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static int compareDesc(LocalDate t1, LocalDate t2) {
		if(t1.equals(t2)){
			return 0;
		} else if(t1.isBefore(t2)) {
			return 1;
		} else {
			return -1;
		}
	}
	
	/**
	 * Compares its two arguments for order. Returns a negative integer, zero, or 
	 * a positive integer as the first argument is less than, equal to, or greater than the second.
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static int compareDesc(LocalTime t1, LocalTime t2) {
		if(t1.equals(t2)){
			return 0;
		} else if(t1.isBefore(t2)) {
			return 1;
		} else {
			return -1;
		}
	}
	
	/**
	 * Compares its two arguments for order. Returns a negative integer, zero, or 
	 * a positive integer as the first argument is less than, equal to, or greater than the second.
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static int compareDesc(LocalDateTime t1, LocalDateTime t2) {
		if(t1.equals(t2)){
			return 0;
		} else if(t1.isBefore(t2)) {
			return 1;
		} else {
			return -1;
		}
	}
	
	/**
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetweenOrEquals(LocalDate t, LocalDate start, LocalDate end) {
		return isAfterOrEquals(t, start) && isBeforeOrEquals(t, end);
	}

	/* Used for birthday
	 * 
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isMonthAndDayBetweenOrEquals(LocalDate t, LocalDate start, LocalDate end) {
		LocalDate month_day_t = LocalDate.of(0, t.getMonthValue(), t.getDayOfMonth());
		LocalDate month_day_start = LocalDate.of(0, start.getMonthValue(), start.getDayOfMonth());
		LocalDate month_day_end = LocalDate.of(0, end.getMonthValue(), end.getDayOfMonth());
		return isAfterOrEquals(month_day_t, month_day_start) && isBeforeOrEquals(month_day_t, month_day_end);
	}
	
	/**
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetweenOrEquals(LocalTime t, LocalTime start, LocalTime end) {
		return isAfterOrEquals(t, start) && isBeforeOrEquals(t, end);
	}

	/**
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetweenOrEquals(LocalDateTime t, LocalDateTime start, LocalDateTime end) {
		return isAfterOrEquals(t, start) && isBeforeOrEquals(t, end);
	}

	/**
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetweenOrEqualsNow(LocalDate start, LocalDate end) {
		return isBetweenOrEquals(LocalDate.now(), start, end);
	}

	/**
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetweenOrEqualsNow(LocalTime start, LocalTime end) {
		return isBetweenOrEquals(LocalTime.now(), start, end);
	}

	/**
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetweenOrEqualsNow(LocalDateTime start, LocalDateTime end) {
		return isBetweenOrEquals(LocalDateTime.now(), start, end);
	}
	
	/**
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetween(LocalDate t, LocalDate start, LocalDate end) {
		return t.isAfter(start) && t.isBefore(end);
	}

	/**
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetween(LocalDateTime t, LocalDateTime start, LocalDateTime end) {
		return t.isAfter(start) && t.isBefore(end);
	}

	/**
	 * @param t
	 * @param start
	 * @param end
	 * @return
	 */
	public static boolean isBetween(LocalTime t, LocalTime start, LocalTime end) {
		return t.isAfter(start) && t.isBefore(end);
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isEqualsOrAfterNow(LocalDate t1) {
		return isAfterOrEquals(t1, LocalDate.now());
	}

	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isEqualsOrAfterNow(LocalDateTime t1) {
		return isAfterOrEquals(t1, LocalDateTime.now());
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isAfterNow(LocalDate t1) {
		return isAfter(t1, LocalDate.now());
	}

	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isAfterNow(LocalDateTime t1) {
		return isAfter(t1, LocalDateTime.now());
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isEqualsOrBeforeNow(LocalDate t1) {
		return isAfterOrEquals(LocalDate.now(), t1);
	}

	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isEqualsOrBeforeNow(LocalDateTime t1) {
		return isAfterOrEquals(LocalDateTime.now(), t1);
	}
	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isBeforeNow(LocalDate t1) {
		return isBefore(t1, LocalDate.now());
	}

	
	/**
	 * @param t1
	 * @return
	 */
	public static boolean isBeforeNow(LocalDateTime t1) {
		return isBefore(t1, LocalDateTime.now());
	}
	
	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBeforeOrEquals(LocalDate t1) {
		return isBeforeOrEquals(t1, LocalDate.now());
	}

	
	/**
	 * @param t1
	 * @param t2
	 * @return
	 */
	public static boolean isBeforeOrEquals(LocalDateTime t1) {
		return isBeforeOrEquals(t1, LocalDateTime.now());
	}
	
	/**
	 * @param datetime
	 * @return
	 */
	public static Date convertToDate(LocalDateTime datetime) {
		return java.sql.Timestamp.valueOf(datetime);	
	}
	
	/**
	 * @param date
	 * @return
	 */
	public static LocalDate convertToLocalDate(Date date) {
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	}
	/**
	 * @param datetime
	 * @return
	 */
	public static LocalDateTime convertToLocalDateTime(Date date) {
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
	}
	
	/**
	 * @param date
	 * @return
	 */
	public static Date convertToDate(LocalDate date) {
		return java.sql.Date.valueOf(date);	
	}

	/**
	 * @param date
	 * @param orDefault
	 * @return
	 */
	public static LocalDateTime parseDateTime(String date, LocalDateTime orDefault) {
		
		try {
			
			if (!date.isEmpty()) {
				orDefault = LocalDateTime.parse(date, FORMAT_DATETIME);
			}
			
		} catch (Exception e) {}
		
		return orDefault;
	}
	
	/**
	 * @param date
	 * @param orDefault
	 * @return
	 */
	public static LocalDate parseDate(String date, LocalDate orDefault) {
		
		try {
			
			if (!date.isEmpty()) {
				orDefault = LocalDate.parse(date, FORMAT_DATE);
			}
			
		} catch (Exception e) {}
		
		return orDefault;
	}
	
	/**
	 * @param date
	 * @param orDefault
	 * @return
	 */
	public static LocalDateTime parseDate(String date, LocalDateTime orDefault) {
		
		try {
			
			if (!date.isEmpty()) {
				orDefault = LocalDate.parse(date, FORMAT_DATE).atTime(0, 0);
			}
			
		} catch (Exception e) {}
		
		return orDefault;
	}

	/**
	 * @param date
	 * @return
	 */
	public static LocalDate toFirstDayOfMonth(LocalDate date) {
		return date.with(firstDayOfMonth());
	}
	
	/**
	 * @param date
	 * @return
	 */
	public static LocalDate toLastDayOfMonth(LocalDate date) {
		return date.with(lastDayOfMonth());
	}

	/**
	 * @param date
	 * @return
	 */
	public static LocalDate toFirstDayOfWeek(LocalDate date) {
		DayOfWeek firstDayOfWeek = WeekFields.ISO.getFirstDayOfWeek();
		return date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
	}
	
	/**
	 * @param date
	 * @return
	 */
	public static LocalDate toLastDayOfWeek(LocalDate date) {
		DayOfWeek latsDayOfWeek = WeekFields.ISO.getFirstDayOfWeek().plus(6);
		return date.with(TemporalAdjusters.nextOrSame(latsDayOfWeek));
	}

	/**
	 * @param datetime
	 * @return
	 */
	public static LocalDateTime toZoneOffsetUTC(LocalDateTime datetime) {
		return toZoneOffset(datetime, ZoneOffset.UTC);
	}
	
	/**
	 * @param datetime
	 * @param zone
	 * @return
	 */
	public static LocalDateTime toZoneOffset(LocalDateTime datetime, ZoneOffset zone) {
		ZonedDateTime utcZoned = datetime.atZone(ZoneId.systemDefault()).withZoneSameInstant(zone);
		return utcZoned.toLocalDateTime();
	}
	
	
	/**
	 * @param xmlGregorianCalendar
	 * @return
	 */
	public static LocalDate XMLGregorianCalendarToLocalDate(XMLGregorianCalendar xmlGregorianCalendar) {
		if(xmlGregorianCalendar == null) {
			return null;
		}
		return LocalDate.of(xmlGregorianCalendar.getYear(), xmlGregorianCalendar.getMonth(), xmlGregorianCalendar.getDay());
	}

	/**
	 * @param xmlGregorianCalendar
	 * @return
	 */
	public static LocalDateTime XMLGregorianCalendarToLocalDateTime(XMLGregorianCalendar xmlGregorianCalendar) {
		if(xmlGregorianCalendar == null) {
			return null;
		}
		return LocalDateTime.of(xmlGregorianCalendar.getYear(), xmlGregorianCalendar.getMonth(), xmlGregorianCalendar.getDay(),
				xmlGregorianCalendar.getHour(), xmlGregorianCalendar.getMinute(), xmlGregorianCalendar.getSecond());
	}

	/**
	 * @param date
	 * @return
	 */
	public static LocalDateTime atStartOfDay(LocalDateTime date) {
		return atStartOfDay(date.toLocalDate());
	}

	/**
	 * @param date
	 * @return
	 */
	public static LocalDateTime atEndOfOfDay(LocalDateTime date) {
		return atEndOfDay(date.toLocalDate());
	}

	/**
	 * @param date
	 * @return
	 */
	public static LocalDateTime atStartOfDay(LocalDate date) {
		return date.atStartOfDay();
	}
	
	/**
	 * @param date
	 * @return
	 */
	public static LocalDateTime atEndOfDay(LocalDate date) {
		return LocalDateTime.of(date, LocalTime.of(23, 59, 59, 999000000));
	}

}
	


