/**
 * 
 */
package com.helios.etl.outer.utils;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 *
 */
public class CompressUtils {

	/**
	 * @param name
	 * @param data
	 * @return
	 * @throws IOException
	 */
	public static byte[] zip(String name, String data) throws IOException {
		
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
        ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream);
        
        zipOutputStream.putNextEntry(new ZipEntry(name));
        zipOutputStream.write(data.getBytes());
        zipOutputStream.closeEntry();
        
        if (zipOutputStream != null) {
            zipOutputStream.finish();
            zipOutputStream.flush();
            IOUtils.closeQuietly(zipOutputStream);
        }
        
        IOUtils.closeQuietly(bufferedOutputStream);
        IOUtils.closeQuietly(byteArrayOutputStream);
        
        return byteArrayOutputStream.toByteArray();
        
	}
}


