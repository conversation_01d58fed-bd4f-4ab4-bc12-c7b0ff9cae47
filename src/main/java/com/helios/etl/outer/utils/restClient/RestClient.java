/**
 * 
 */
package com.helios.etl.outer.utils.restClient;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.List;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public abstract class RestClient {

	
	/**
	 * 
	 */
	@Getter
	protected RestTemplate client;

	/**
	 * 
	 */
	@Getter
	protected String endpoint;
	
	/**
	 * 
	 */
	protected void createClient() {
		createClient(null);
	}
	
	/**
	 * @param interceptors
	 */
	protected void createClient(List<ClientHttpRequestInterceptor> interceptors) {
		client = new RestTemplate();
		
		if(interceptors != null && interceptors.size() > 0) {
			client.getInterceptors().addAll(interceptors);
		}
		
		client.getInterceptors().add(new LoggingRequestInterceptor());
		client.getMessageConverters().add(0, new StringHttpMessageConverter(Charset.forName("UTF-8")));
	}
	
	/**
	 * @return
	 */
	protected HttpHeaders defaultHeader() {
		
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(Collections.singletonList(MediaType.ALL));
		
		return headers;
	}

	/**
	 * @param route
	 * @return
	 */
	protected UriComponentsBuilder defaultBuilder(String route) {
		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(String.format("%s%s", endpoint, route));
		return builder;
	}

	/**
	 * @param route
	 * @return
	 * @throws IOException
	 */
	public <R> ResponseEntity<Object> post(String route) throws Exception {
		return post(route, new Object(), defaultHeader(), Object.class);
	}
	
	/**
	 * @param route
	 * @param request
	 * @return
	 * @throws IOException
	 */
	public <R> ResponseEntity<Object> post(String route, R request) throws Exception {
		return post(route, request, defaultHeader(), Object.class);
	}
	
	/**
	 * @param route
	 * @param headers
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public <R> ResponseEntity<Object> post(String route, HttpHeaders headers, R request) throws Exception {
		return post(route, request, headers, Object.class);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <R, T> ResponseEntity<T> post(String route, R request, Class<T> responseType) throws Exception {
		return post(route, request, defaultHeader(), responseType);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> post(String route, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.POST, new HttpEntity<Object>(defaultHeader()), responseType);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <R, T> ResponseEntity<T> post(String route, R request, HttpHeaders headers, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.POST, new HttpEntity<R>(request, headers), responseType);
	}

	/**
	 * @param route
	 * @return
	 * @throws IOException
	 */
	public ResponseEntity<Object> get(String route) throws Exception {
		return get(route, defaultHeader(), Object.class);
	}
	
	/**
	 * @param builder
	 * @return
	 * @throws IOException
	 */
	public ResponseEntity<Object> get(UriComponentsBuilder builder) throws Exception {
		return get(builder, defaultHeader(), Object.class);
	}

	/**
	 * @param route
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> get(String route, Class<T> responseType) throws Exception {
		return get(route, defaultHeader(), responseType);
	}

	/**
	 * @param builder
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> get(UriComponentsBuilder builder, Class<T> responseType) throws Exception {
		return get(builder, defaultHeader(), responseType);
	}
	
	/**
	 * @param route
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> get(String route, HttpHeaders headers, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.GET, new HttpEntity<Object>(headers), responseType);
	}
	
	/**
	 * @param builder
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> get(UriComponentsBuilder builder, HttpHeaders headers, Class<T> responseType) throws Exception {
		return exchange(builder, HttpMethod.GET, new HttpEntity<Object>(headers), responseType);
	}

	/**
	 * @param route
	 * @return
	 * @throws IOException
	 */
	public <R> ResponseEntity<Object> put(String route) throws Exception {
		return put(route, new Object(), defaultHeader(), Object.class);
	}
	
	/**
	 * @param route
	 * @param request
	 * @return
	 * @throws IOException
	 */
	public <R> ResponseEntity<Object> put(String route, R request) throws Exception {
		return put(route, request, defaultHeader(), Object.class);
	}
	
	/**
	 * @param route
	 * @param headers
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public <R> ResponseEntity<Object> put(String route, HttpHeaders headers, R request) throws Exception {
		return put(route, request, headers, Object.class);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <R, T> ResponseEntity<T> put(String route, R request, Class<T> responseType) throws Exception {
		return put(route, request, defaultHeader(), responseType);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> put(String route, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.PUT, new HttpEntity<Object>(defaultHeader()), responseType);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <R, T> ResponseEntity<T> put(String route, R request, HttpHeaders headers, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.PUT, new HttpEntity<R>(request, headers), responseType);
	}

	/**
	 * @param route
	 * @return
	 * @throws IOException
	 */
	public <R> ResponseEntity<Object> delete(String route) throws Exception {
		return delete(route, new Object(), defaultHeader(), Object.class);
	}

	/**
	 * @param route
	 * @param request
	 * @return
	 * @throws IOException
	 */
	public <R> ResponseEntity<Object> delete(String route, R request) throws Exception {
		return delete(route, request, defaultHeader(), Object.class);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <R, T> ResponseEntity<T> delete(String route, R request, Class<T> responseType) throws Exception {
		return delete(route, request, defaultHeader(), responseType);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <T> ResponseEntity<T> delete(String route, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.DELETE, new HttpEntity<Object>(defaultHeader()), responseType);
	}
	
	/**
	 * @param route
	 * @param request
	 * @param headers
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	public <R, T> ResponseEntity<T> delete(String route, R request, HttpHeaders headers, Class<T> responseType) throws Exception {
		return exchange(route, HttpMethod.DELETE, new HttpEntity<R>(request, headers), responseType);
	}
	
	/**
	 * @param route
	 * @param method
	 * @param entity
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	protected <T> ResponseEntity<T> exchange(String route, HttpMethod method, HttpEntity<?> entity, Class<T> responseType) throws Exception {

		ResponseEntity<T> response = client.exchange(
			defaultBuilder(route).build().toUri(), 
			method, 
			entity, 
			responseType
		);
		
		return response;
	}
	
	/**
	 * @param builder
	 * @param method
	 * @param entity
	 * @param responseType
	 * @return
	 * @throws IOException
	 */
	protected <T> ResponseEntity<T> exchange(UriComponentsBuilder builder, HttpMethod method, HttpEntity<?> entity, Class<T> responseType) throws Exception {

		ResponseEntity<T> response = client.exchange(
			builder.build().toUri(), 
			method, 
			entity, 
			responseType
		);
		
		return response;
	}
	
}


