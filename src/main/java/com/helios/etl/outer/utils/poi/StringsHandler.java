/**
 * 
 */
package com.helios.etl.outer.utils.poi;

import java.util.regex.Pattern;

import org.apache.poi.xwpf.usermodel.XWPFRun;

import com.helios.etl.outer.utils.FindReplaceMapper;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public class StringsHandler implements ContentHandler{

	/**
	 * 
	 */
	@Getter
	protected String prefix = "$" + "{"; // Evite un avertissemennt el-sintax;

	/**
	 * 
	 */
	@Getter
	protected String suffix = "}";
	
	/**
	 * 
	 */
	@Getter
	protected Pattern pattern = Pattern.compile(Pattern.quote("$" + "{") + "([^}]+?)" + Pattern.quote("}"), Pattern.DOTALL);
	
	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.poi.ContentHandler#replace(org.apache.poi.xwpf.usermodel.XWPFRun, java.lang.String, java.lang.String, java.lang.String, fr.actuelburo.utils.FindReplaceMapper)
	 */
	@Override
	public String replace(XWPFRun run, String text, String key, String needle, FindReplaceMapper mapper) {

		String value = mapper.map(key);
		
		if(value == null) {
			value = "";
		}
		
		text = text.replace(needle, value);
		run.setText(text, 0);
		return text;
	}

}


