/**
 * 
 */
package com.helios.etl.outer.utils.regex;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *
 */
public class RegexPatternMatcher extends TemplatePatternMatcher {
		
	/**
	 * 
	 */
	protected Pattern pattern;
	
	/**
	 * Compiles the given regular expression into a {@link java.util.regex.Pattern}.
	 * @param rule
	 * @see java.util.regex.Pattern
	 */
	public RegexPatternMatcher(String rule) {
		pattern = Pattern.compile(rule);
	}
	
	/**
	 * Compiles the given regular expression into a {@link java.util.regex.Pattern} with the given flags.
	 * @param rule
     * @param  flags
     *         Match flags, a bit mask that may include
     *         {@link #CASE_INSENSITIVE}, {@link #MULTILINE}, {@link #DOTALL},
     *         {@link #UNICODE_CASE}, {@link #CANON_EQ}, {@link #UNIX_LINES},
     *         {@link #LITERAL}, {@link #UNICODE_CHARACTER_CLASS}
     *         and {@link #COMMENTS}
	 * @see java.util.regex.Pattern
	 */
	public RegexPatternMatcher(String rule, int flags) {
		pattern = Pattern.compile(rule, flags);
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.regex.PatternMatcher#matches(java.lang.String)
	 */
	@Override
	public boolean matches(String input) {
        Matcher m = pattern.matcher(input);
        return m.matches();
	}


}


