/**
 * 
 */
package com.helios.etl.outer.utils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class TimeSlot {

	/**
	 * 
	 */
	@Getter
	protected LocalDateTime start;

	/**
	 * 
	 */
	@Getter
	protected LocalDateTime end;

	/**
	 * 
	 */
	@Getter
	protected long duration;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected Object meta;

	/**
	 */
	public TimeSlot() {
	}

	/**
	 * @param time
	 * @return
	 */
	public static LocalDateTime timeFromNow(LocalTime time) {
		return LocalDateTime.now().with(time);
	}
	
	/**
	 * @param debut
	 * @param fin
	 */
	public TimeSlot(LocalDateTime start, long duration) {
		this.start = start;
		this.end = start.plusMinutes(duration -1);
		this.duration = duration;
	}
	
	/**
	 * @param start
	 * @param duration
	 * @param meta
	 */
	public TimeSlot(LocalDateTime start, long duration, Object meta) {
		this(start, duration);
		this.meta = meta;
	}

	/**
	 * @param start
	 * @param end
	 */
	public TimeSlot(LocalDateTime start, LocalDateTime end) {
		reset(start, end);
	}

	/**
	 * @param start
	 * @param end
	 * @param meta
	 */
	public TimeSlot(LocalDateTime start, LocalDateTime end, Object meta) {
		reset(start, end);
		this.meta = meta;
	}
	
	/**
	 * @param start
	 * @param end
	 */
	public TimeSlot(LocalTime start, LocalTime end) {
		this(timeFromNow(start), timeFromNow(end));
	}

	/**
	 * @param start
	 * @param duration
	 */
	public TimeSlot(LocalTime start, long duration) {
		this(timeFromNow(start), duration);
	}

	/**
	 * @param start
	 * @param duration
	 * @param meta
	 */
	public TimeSlot(LocalTime start, long duration, Object meta) {
		this(timeFromNow(start), duration);
		this.meta = meta;
	}

	/**
	 * 
	 */
	public void normalize() {
		if(start.isAfter(end)) {
			duration = Math.abs(start.until(end, ChronoUnit.MINUTES));
			start = end;
			end = start.plusMinutes(duration -1);
		}
	}

	/**
	 * 
	 */
	public boolean isNormalized() {
		return TemporalUtils.isBeforeOrEquals(start, end) && duration == start.until(end, ChronoUnit.MINUTES) +1;
	}
	
	/**
	 * @param slot
	 * @return
	 */
	public boolean isIntersect(TimeSlot slot) {
		return TemporalUtils.isBeforeOrEquals(start, slot.end) && TemporalUtils.isAfterOrEquals(end, slot.start);
	}
	
	/**
	 * @param slot
	 * @return
	 */
	public boolean isBefore(TimeSlot slot) {
		return TemporalUtils.isBeforeOrEquals(end, slot.start);
	}
	
	/**
	 * @param slot
	 * @return
	 */
	public boolean isAfter(TimeSlot slot) {
		return TemporalUtils.isBeforeOrEquals(slot.end, start);
	}
	
	/**
	 * @return
	 */
	public long distance(TimeSlot slot) {
		
		if(isIntersect(slot)) {
			return 0;
		}
		
		if(isBefore(slot)){
			return end.until(slot.start, ChronoUnit.MINUTES);
			
		} else if(isAfter(slot)){
			return slot.end.until(start, ChronoUnit.MINUTES);
			
		} else {
			return 0; // Intersect
		}
		
	}
	
	/**
	 * @return
	 */
	public long distanceToStart(LocalDateTime time) {
		return time.until(start, ChronoUnit.MINUTES);
	}
	
	/**
	 * @return
	 */
	public long distanceToEnd(LocalDateTime time) {
		return time.until(end, ChronoUnit.MINUTES);
	}
	
	/**
	 * 
	 */
	public TimeSlot duplicate() {
		TimeSlot slot = new TimeSlot(start, duration);
		slot.setMeta(meta);
		return slot;
	}

	/**
	 * @param start
	 * @param end
	 */
	public void reset(LocalDateTime start, LocalDateTime end) {
		this.start = start;
		this.end = end;
		duration = start.until(end, ChronoUnit.MINUTES) +1;
	}
	
	/**
	 */
	public void refreshDuration() {
		duration = start.until(end, ChronoUnit.MINUTES) +1;
	}
	
	/**
	 * @param start
	 * @param end
	 */
	public void reset(LocalTime start, LocalTime end) {
		reset(timeFromNow(start), timeFromNow(end));
	}
	
	/**
	 * @param time
	 */
	public void startTo(LocalDateTime time) {
		this.start = time;
		this.end = start.plusMinutes(duration -1);
	}
	
	/**
	 * @param time
	 */
	public void startTo(LocalTime time) {
		startTo(timeFromNow(time));
	}
	
	/**
	 * @param time
	 */
	public void endAt(LocalDateTime time) {
		this.end = time;
		this.start = end.minusMinutes(duration -1);
	}
	
	/**
	 * @param time
	 */
	public void endAt(LocalTime time) {
		endAt(timeFromNow(time));
	}
	
	/**
	 * @param time
	 */
	public void expandFrom(LocalDateTime time) {
		this.start = time;
		duration = start.until(end, ChronoUnit.MINUTES) +1;
	}
	
	/**
	 * @param time
	 */
	public void expandTo(LocalDateTime time) {
		this.end = time;
		duration = start.until(end, ChronoUnit.MINUTES) +1;
	}
	
	/**
	 * @param time
	 * @return
	 */
	public boolean isStartSame(LocalTime time) {
		return time.until(start, ChronoUnit.MINUTES) == 0;
	}
	
	/**
	 * @param time
	 * @return
	 */
	public boolean isEndSame(LocalTime time) {
		return time.until(end, ChronoUnit.MINUTES) == 0;
	}
	
	/**
	 * 
	 */
	public void next() {
		start = start.plusMinutes(duration);
		end = start.plusMinutes(duration -1);
	}
	
	/**
	 * 
	 */
	public void previous() {
		start = start.minusMinutes(duration);
		end = start.plusMinutes(duration -1);
	}
	
	/**
	 * @param amount
	 */
	public void shift(int amount) {
		start = start.plusMinutes(amount);
		end = start.plusMinutes(duration -1);
	}
	
	/**
	 * @param amount
	 */
	public void unshift(int amount) {
		start = start.minusMinutes(amount);
		end = start.plusMinutes(duration -1);
	}
	
	/**
	 * @param time
	 * @return
	 */
	public boolean contains(LocalDateTime time) {
		return TemporalUtils.isBetweenOrEquals(time, start, end);
	}
	
	/**
	 * @param time
	 * @return
	 */
	public boolean contains(LocalTime time) {
		return contains(timeFromNow(time));
	}
	
	/**
	 * Renvoie vrai si la fin du slot est avant {@code time}
	 * @param time
	 * @return
	 */
	public boolean isBefore(LocalDateTime time) {
		return end.isBefore(time);
	}
	
	/**
	 * Renvoie vrai si la fin du slot est avant {@code time}
	 * @param time
	 * @return
	 */
	public boolean isBefore(LocalTime time) {
		return isBefore(timeFromNow(time));
	}
	
	/**
	 * Renvoie vrai si le dÃ©but du slot est aprÃ¨s {@code time}
	 * @param time
	 * @return
	 */
	public boolean isAfter(LocalDateTime time) {
		return start.isAfter(time);
	}
	
	/**
	 * Renvoie vrai si le dÃ©but du slot est aprÃ¨s {@code time}
	 * @param time
	 * @return
	 */
	public boolean isAfter(LocalTime time) {
		return isAfter(timeFromNow(time));
	}
	
	/**
	 * Renvoie vrai si la fin du slot est Ã©gale ou avant {@code time}
	 * @param time
	 * @return
	 */
	public boolean isBeforeOrEquals(LocalDateTime time) {
		return TemporalUtils.isBeforeOrEquals(end, time);
	}
	
	/**
	 * Renvoie vrai si la fin du slot est Ã©gale ou avant {@code time}
	 * @param time
	 * @return
	 */
	public boolean isBeforeOrEquals(LocalTime time) {
		return isBeforeOrEquals(timeFromNow(time));
	}
	
	/**
	 * Renvoie vrai si le dÃ©but du slot est Ã©gal ou aprÃ¨s {@code time}
	 * @param time
	 * @return
	 */
	public boolean isAfterOrEquals(LocalDateTime time) {
		return TemporalUtils.isAfterOrEquals(start, time);
	}

	/**
	 * @param time
	 */
	public boolean canReachStart(LocalDateTime time) {
		
		long until = Math.abs(time.until(start, ChronoUnit.MINUTES));
		
		if(until > duration) {
			return until % duration == 0;
		} else {
			return false;
		}
		
	}

	/**
	 * @param time
	 */
	public boolean canReachStart(LocalTime time) {
		return canReachStart(timeFromNow(time));
	}
	
	/**
	 * @param time
	 */
	public boolean canReachStart(LocalDateTime time, int amount) {
		if(amount <= 1) {
			return true;
		}
		
		long until = Math.abs(time.until(start, ChronoUnit.MINUTES));
		return until % amount == 0;
	}
	
	/**
	 * @param time
	 */
	public boolean canReachStart(LocalTime time, int amount) {
		return canReachStart(timeFromNow(time), amount);
	}
	
	/**
	 * @param time
	 */
	public void moveTo(LocalDateTime time) {
		
		while(!contains(time)) {
			if(isBefore(time)) {
				next();
			} else {
				previous();
			}
		}
		
	}
	
	/**
	 * @param time
	 */
	public void moveTo(LocalTime time) {
		moveTo(timeFromNow(time));
	}
	
	/**
	 * @param time
	 */
	public void shiftTo(LocalDateTime time, int amount) {
		
		while(!contains(time)) {
			if(isBefore(time)) {
				shift(amount);
			} else {
				unshift(amount);
			}
		}
		
	}
	
	/**
	 * @param time
	 */
	public void shiftTo(LocalTime time, int amount) {
		shiftTo(timeFromNow(time), amount);
	}

	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * @param time
	 */
	public void closestStart(LocalDateTime time) {
		
		moveTo(time);
		
		if(start.until(time, ChronoUnit.MINUTES) > duration /2) {
			next();
		}
		
	}

	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * @param time
	 */
	public void closestStart(LocalTime time) {
		closestStart(timeFromNow(time));
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * Avec obligation que l'heure de dÃ©but se trouve avant {@code time}
	 * @param time
	 */
	public void closestStartBefore(LocalDateTime time) {
		
		moveTo(time);
		
		if(start.until(time, ChronoUnit.MINUTES) > duration /2) {
			next();
		}
		
		//si on dÃ©passe l'heure indiquÃ©e, on revient sur le slot prÃ©cÃ©dent
		if (start.compareTo(time) > 0) {
			previous();
		}
		
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * Avec obligation que l'heure de dÃ©but se trouve avant {@code time}
	 * @param time
	 */
	public void closestStartBefore(LocalTime time) {
		closestStartBefore(timeFromNow(time));
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * @param time
	 */
	public void closestStartAfter(LocalDateTime time) {
		
		moveTo(time);
		
		if(start.until(time, ChronoUnit.MINUTES) > duration /2 || start.compareTo(time) < 0 ) {
			next();
		}
		
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * @param time
	 */
	public void closestStartAfter(LocalTime time) {
		closestStartAfter(timeFromNow(time));
		
	}

	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * en dÃ©calant le slot de  {@code amount} minutes
	 * @param time
	 * @param amount
	 * @param limit
	 */
	public void closestStart(LocalDateTime time, int amount) {
		shiftTo(time, amount);
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * en dÃ©calant le slot de  {@code amount} minutes
	 * @param time
	 * @param amount
	 * @param limit
	 */
	public void closestStart(LocalTime time, int amount) {
		closestStart(timeFromNow(time), amount);
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * en dÃ©calant le slot de  {@code amount} minutes sans dÃ©passer {@limit}
	 * @param time
	 * @param amount
	 * @param limit
	 */
	public void closestStart(LocalDateTime time, int amount, LocalDateTime limit, long rdvDuration) {
		
		// On dÃ©place le slot pour encadrer l'heure demandÃ©e
		LocalDateTime _start = start;
		
		shiftTo(time, amount);
		
		// On test si on ne dÃ©passe pas la limite. Si oui il n'y a pas de solution
		if(limit != null && !isBeforeOrEquals(limit)) {
			startTo(_start);
			return;
		}
		
		// on essaye maintenant de s'en approcher le plus possible
//		while(start.until(time, ChronoUnit.MINUTES) >= amount) {
//			shift(amount);
//			if(limit != null && !isBeforeOrEquals(limit)) {
//				unshift(amount);
//				break;
//			}
//		}
		
		boolean closetSlotFund = false;
		while (!closetSlotFund) {
			TimeSlot nextSlot = this.duplicate();
			nextSlot.shift(amount);
			long currentMinutesUntilTime = Math.abs(start.until(time, ChronoUnit.MINUTES));
			long nextMinutesUntilTime = Math.abs(nextSlot.start.until(time, ChronoUnit.MINUTES));

			if (currentMinutesUntilTime <= nextMinutesUntilTime) {
				closetSlotFund = true;
			} else {
				shift(amount);
				if (limit != null && !isBeforeOrEquals(limit)) {
					TimeSlot nextSlotUntilRdvDuration = new TimeSlot(this.start, rdvDuration);
					if (!nextSlotUntilRdvDuration.isBeforeOrEquals(limit)) {
						unshift(amount);
						break;						
					} else {
						end = limit;
					}
				}
			}
		}
		
		
	}
	
	/**
	 * Recherche le slot dont le debut est le plus proche de {@code time}
	 * en dÃ©calant le slot de  {@code amount} minutes sans dÃ©passer {@limit}
	 * @param time
	 * @param amount
	 * @param limit
	 */
	public void closestStart(LocalTime time, int amount, LocalTime limit, long rdvDuration) {
		closestStart(timeFromNow(time), amount, timeFromNow(limit), rdvDuration);
	}

	/**
	 * Recherche le slot dont la fin est le plus proche de {@code time}
	 * @param time
	 */
	public void closestEnd(LocalDateTime time) {
		
		moveTo(time);
		
		if(time.until(end, ChronoUnit.MINUTES) > duration /2) {
			previous();
		}
		
	}

	/**
	 * Recherche le slot dont la fin est le plus proche de {@code time}
	 * @param time
	 */
	public void closestEnd(LocalTime time) {
		closestEnd(timeFromNow(time));
	}
	
	/**
	 * Recherche le slot dont la fin est le plus proche de {@code time}
	 * en dÃ©calant le slot de  {@code amount} minutes
	 * @param time
	 * @param amount
	 * @param limit
	 */
	public void closestEnd(LocalDateTime time, int amount) {
		shiftTo(time, amount);
		
		// on essaye maintenant de s'en approcher le plus possible
		while(end.until(time, ChronoUnit.MINUTES) < 0 || end.until(time, ChronoUnit.MINUTES) >= amount) {
			unshift(amount);
		}
		
	}
	
	/**
	 * Recherche le slot dont la fin est le plus proche de {@code time}
	 * en dÃ©calant le slot de  {@code amount} minutes
	 * @param time
	 * @param amount
	 * @param limit
	 */
	public void closestEnd(LocalTime time, int amount) {
		closestEnd(timeFromNow(time), amount);
	}

	/**
	 * Recherche le slot dont la fin est le plus proche de {@code time}
	 * @param time
	 */
	public void closestEndAfter(LocalDateTime time) {
		
		LocalDateTime timeToMove = time.minusMinutes(duration);
		moveTo(timeToMove);
		
		if(end.until(time, ChronoUnit.MINUTES) > duration /2 || end.compareTo(time) < 0) {
			next();
		}
		
	}

	/**
	 * Recherche le slot dont la fin est le plus proche de {@code time}
	 * @param time
	 */
	public void closestEndAfter(LocalTime time) {
		closestEndAfter(timeFromNow(time));
	}

	/**
	 * DÃ©place le slot jusqu'Ã  limit sans la dÃ©passer Ã  la fin
	 * @param limit
	 */
	public void lastLimitEnd(LocalDateTime limit) {
		moveTo(limit);
		if(TemporalUtils.isAfter(end, limit)) {
			previous();
		}
	}

	/**
	 * DÃ©place le slot jusqu'Ã  limit sans la dÃ©passer Ã  la fin
	 * @param limit
	 */
	public void lastLimitEnd(LocalTime limit) {
		lastLimitEnd(timeFromNow(limit));
	}
	
	@Override
	public String toString() {
		return String.format(
			"TimeSlot %s -> %s (%d) %s", 
			start.toString().replace("T", " "), 
			end.toString().replace("T", " "), 
			duration, meta == null ? "" : meta
		);
	}
	
}


