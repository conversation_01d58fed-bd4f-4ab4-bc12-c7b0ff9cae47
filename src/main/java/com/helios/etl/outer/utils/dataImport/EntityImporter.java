/**
 * 
 */
package com.helios.etl.outer.utils.dataImport;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Hashtable;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class EntityImporter<T extends Importable<T>> {
	
	/**
	 * 
	 */
	private Hashtable<EntityState, Collection<T>> analyses;
	
	/**
	 * 
	 */
	private Hashtable<String, T> current;
	
	/**
	 * 
	 */
	private boolean full = false;
	
	/**
	 * 
	 */
	private boolean forceUpdate = false;
	
	/**
	 * 
	 */
	private EntityState[] states;

	/**
	 * @param full
	 * @param states
	 */
	public EntityImporter(boolean full, EntityState... states){
		this(full, false, states);
	}
	
	/**
	 * @param full
	 * @param forceUpdate
	 * @param states
	 */
	public EntityImporter(boolean full, boolean forceUpdate, EntityState... states){
		analyses = new Hashtable<>();
		analyses.put(EntityState.UNCHANGED, new ArrayList<>());
		analyses.put(EntityState.ADDED, new ArrayList<>());
		analyses.put(EntityState.UPDATED, new ArrayList<>());
		analyses.put(EntityState.DELETED, new ArrayList<>());
		
		this.full = full;
		this.forceUpdate = forceUpdate;
		this.states = states;
	}
	
	/**
	 * @return
	 */
	public Hashtable<EntityState, Collection<T>> analyses(){
		return analyses;
	}
	
	/**
	 * Dresser une carte des Ã©lÃ©ments actuels
	 * @param elements
	 */
	public void from(Collection<T> elements) {
		
		current = new Hashtable<>();
		elements.forEach(element -> {
			current.put(element.getCode(), element);
		});
		
	}
	
	/**
	 * Passer en revue les Ã©lÃ©ments Ã  traiter
	 * @param elements
	 */
	public void compare(Collection<T> elements) {
		
		check(elements);
		
		for(T element : elements) {
			
			if(current.containsKey(element.getCode())) {

				element.setOid(current.get(element.getCode()).getOid());
				
				if(hasState(EntityState.UPDATED)) {
					
					if(forceUpdate || current.get(element.getCode()).isUpdate(element)){
						analyses.get(EntityState.UPDATED).add(element);
					} else {
						analyses.get(EntityState.UNCHANGED).add(element);
					}
					
				} else {
					analyses.get(EntityState.UNCHANGED).add(element);
				}

				current.remove(element.getCode());
				
			} else {

				element.setOid(-1);
				
				if(hasState(EntityState.ADDED)) {
					analyses.get(EntityState.ADDED).add(element);
				}
			}
		}
		
		// 3- Si l'import est complet, les restants sont Ã  supprimer
		for(String key : current.keySet()) {
			
			// On ne veut voir que les changements de la liste envoyÃ©e si elle n'est pas complÃ¨te
			// ainsi, on ne traite que que les Ã©lÃ©ments de la liste
			if(full) {
				if(hasState(EntityState.DELETED)) {
					analyses.get(EntityState.DELETED).add(current.get(key));
				} else {
					analyses.get(EntityState.UNCHANGED).add(current.get(key));
				}
			}
		
		}
		
	}
	
	/**
	 * Effectue des vÃ©rification sur la structure des donnÃ©es Ã  traiter
	 * - dÃ©tection des doublons
	 * @param elements
	 */
	public void check(Collection<T> elements) {
		
		List<String> codes = new ArrayList<>();
		
		for(T element : elements) {
			
			if(codes.contains(element.getCode())) {
				throw new IllegalArgumentException("Duplicate element code : " + element.getCode());
			}
			
			codes.add(element.getCode());
		}
	}
	
	/**
	 * @param consumer
	 */
	public void forEach(EntityConsumer<T> consumer) {
		notifyConsumer(EntityState.ADDED, consumer);
		notifyConsumer(EntityState.UPDATED, consumer);
		notifyConsumer(EntityState.DELETED, consumer);
	}
	
	/**
	 * @param state
	 * @param consumer
	 */
	private void notifyConsumer(EntityState state, EntityConsumer<T> consumer) {
		
		if(hasState(state) && analyses.containsKey(state)) {
			for(T element : analyses.get(state)) {
				consumer.accept(element, state);
			}
		}
		
	}
	
	/**
	 * @param state
	 * @return
	 */
	private boolean hasState(EntityState state) {
		
		for(EntityState _state : states) {
			if(_state.equals(state)) {
				return true;
			}
		}
		return false;
	}

}


