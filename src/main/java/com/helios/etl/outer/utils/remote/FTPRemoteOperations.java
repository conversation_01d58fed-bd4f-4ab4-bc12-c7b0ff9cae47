/**
 * 
 */
package com.helios.etl.outer.utils.remote;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

/**
 * <AUTHOR>
 *
 */
public class FTPRemoteOperations extends RemoteOperations {
	
	/**
	 * @param dock
	 */
	public FTPRemoteOperations(RemoteDock dock) {
		this.dock = dock;
	}

	/**
	 * @param filepath
	 * @param file
	 * @return
	 * @throws IOException 
	 * @throws RemoteDockException 
	 */
	@Override
	public void transferFile(String filepath, File file) throws IOException, RemoteDockException {
		
		Path path = Paths.get(filepath);
		path.normalize();
		if(!path.startsWith("/")) {
			path = Paths.get("/").resolve(path);
		}
		
		String filename = path.getFileName().toString();
		String workingDirectory = FilenameUtils.separatorsToUnix(path.getParent().toString());
		
		FTPClient client = new FTPClient();
		client.connect(dock.getEndpoint(), dock.getPort());
	
		int reply = client.getReplyCode();
		if (!FTPReply.isPositiveCompletion(reply)) {
			client.disconnect();
			throw new RemoteDockException("Exception in connecting to FTP Server");
		}
	
		client.login(dock.getUsername(), dock.getPassword());
		client.setFileType(FTP.BINARY_FILE_TYPE);
		client.enterLocalPassiveMode();
		client.changeWorkingDirectory(workingDirectory);
	
		InputStream stream = new BufferedInputStream(new FileInputStream(file));
		client.storeFile(filename, stream);
		stream.close();
	
		client.logout();
		client.disconnect();
	
	}
	
}


