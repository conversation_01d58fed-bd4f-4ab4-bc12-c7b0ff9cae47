/**
 * 
 */
package com.helios.etl.outer.utils.regex;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class StringPatternMatcher extends TemplatePatternMatcher {

	public static final int EXACT = 1;
	public static final int START = 10;
	public static final int END = 20;
	public static final int CONTAIN = 30;
	
	public static final char MARK_START = '>';
	public static final char MARK_END = '<';
	public static final char MARK_CONTAIN = '|';
	
	/**
	 * 
	 */
	@Getter
	@Setter
	protected int mode = EXACT;
	
	/**
	 * 
	 */
	protected String pattern;
	
	/**
	 * @param rule
	 */
	public StringPatternMatcher(String rule) {
		int mode = EXACT;
		char first = rule.charAt(0);
		
		switch(first) {
			case MARK_START:
				mode = START;
			break;
			
			case MARK_END:
				mode = END;
			break;
			
			case MARK_CONTAIN:
				mode = CONTAIN;
			break;
		}

		initialize(rule, mode);
	}
	
	/**
	 * @param rule
	 */
	public StringPatternMatcher(String rule, int mode) {
		initialize(rule, mode);
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.regex.PatternMatcher#matches(java.lang.String)
	 */
	@Override
	public boolean matches(String input) {
		
		switch(mode) {
		
			case EXACT:
				return input.equals(pattern);
				
			case START:
				return input.startsWith(pattern);
				
			case END:
				return input.endsWith(pattern);
				
			case CONTAIN:
				return input.contains(pattern);
				
			default : 
				return false;
		}
		
	}

	/**
	 * @param rule
	 * @param mode
	 */
	protected void initialize(String rule, int mode) {
		
		switch(mode) {
		
			case EXACT:
				this.pattern = rule;
				this.rule = rule;
			break;
				
			case START:
				initializePrefixed(rule, MARK_START);
			break;
				
			case END:
				initializePrefixed(rule, MARK_END);
			break;
			
			case CONTAIN:
				initializePrefixed(rule, MARK_CONTAIN);
			break;

		}
		
		this.mode = mode;
	}
	
	/**
	 * Check if rule starts with prefix, with adjusement if needed and defines pattern and rule
	 * @param rule
	 * @param prefix
	 */
	protected void initializePrefixed(String rule, Character prefix) {
		
		if(rule.charAt(0) == prefix) {
			this.pattern = rule.substring(1);
		} else {
			rule = "|" + rule;
			this.pattern = rule;
		}
		
		this.rule = rule;
		
	}
}


