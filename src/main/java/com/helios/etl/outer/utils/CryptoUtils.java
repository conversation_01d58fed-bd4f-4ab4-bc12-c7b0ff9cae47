/**
 * 
 */
package com.helios.etl.outer.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Signature;
import java.security.SignatureException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.data.util.Pair;

/**
 * <AUTHOR>
 *
 */
public class CryptoUtils {
	
	/**
	 * 
	 */
	public static final String HASH_SHA1 = "hashSHA1";

	/**
	 * 
	 */
	public static final String HASH_SHA256 = "hashSHA256";

	/**
	 * 
	 */
	public static final String HASH_SHA512 = "hashSHA512";

	/**
	 * 
	 */
	public static final String HMAC_SHA256 = "hmacSHA256";

	/**
	 * 
	 */
	public static final String HMAC_SHA256_BASE64 = "hmacSHA256Base64";

	/**
	 * 
	 */
	public static final String BLOWFISH = "blowfish";
	
	/**
	 * @param bytes
	 * @param algorithm
	 * @return
	 */
	public static String hashSHA(byte[] bytes, String algorithm) {

		try {
			MessageDigest md = MessageDigest.getInstance(algorithm);
			md.update(bytes, 0, bytes.length);
			byte[] hash = md.digest();
			return StringUtils.convertToHex(hash);
			
		} catch (NoSuchAlgorithmException e) {
			final String errmsg = "NoSuchAlgorithmException: " + e;
			return errmsg;
		}
		
	}

	/**
	 * @param file
	 * @param algorithm
	 * @return
	 */
	public static String hashSHA(File file, String algorithm) {

		try {

			MessageDigest md = MessageDigest.getInstance(algorithm);
			
			FileInputStream fis = new FileInputStream(file);
			byte[] byteArray = new byte[1024];
			int bytesCount = 0;
			
			while ((bytesCount = fis.read(byteArray)) != -1) {
				md.update(byteArray, 0, bytesCount);
			};
			
			fis.close();
			
			byte[] hash = md.digest();
			return StringUtils.convertToHex(hash);
			
		} catch (NoSuchAlgorithmException e) {
			final String errmsg = "NoSuchAlgorithmException: " + e;
			return errmsg;
			
		} catch (IOException e) {
			final String errmsg = "IOException: " + e;
			return errmsg;
		}
		
	}
	
	/**
	 * @param bytes
	 * @return
	 */
	public static String hashSHA1(byte[] bytes) {
		return hashSHA(bytes, "SHA-1");
	}
	
	/**
	 * @param text
	 * @return
	 */
	public static String hashSHA1(String text) {
		return hashSHA(text.getBytes(), "SHA-1");
	}

	/**
	 * @param file
	 * @return
	 */
	public static String hashSHA1(File file) {
		return hashSHA(file, "SHA-1");
	}

	/**
	 * @param bytes
	 * @return
	 */
	public static String hashSHA256(byte[] bytes) {
		return hashSHA(bytes, "SHA-256");
	}
	
	/**
	 * @param text
	 * @return
	 */
	public static String hashSHA256(String text) {
		return hashSHA(text.getBytes(), "SHA-256");
	}

	/**
	 * @param file
	 * @return
	 */
	public static String hashSHA256(File file) {
		return hashSHA(file, "SHA-256");
	}

	/**
	 * @param bytes
	 * @return
	 */
	public static String hashSHA512(byte[] bytes) {
		return hashSHA(bytes, "SHA-512");
	}
	
	/**
	 * @param text
	 * @return
	 */
	public static String hashSHA512(String text) {
		return hashSHA(text.getBytes(), "SHA-512");
	}

	/**
	 * @param file
	 * @return
	 */
	public static String hashSHA512(File file) {
		return hashSHA(file, "SHA-512");
	}
	
	/**
	 * Computes the seal
	 * 
	 * @param Data      the data to seal
	 * @param secretKey the secret key
	 * @return hex representation of the seal, two chars per byte.
	 */
	public static String hmacSHA256Seal(String data, String secretKey) throws Exception {
		return hmacSHA256Seal(data.getBytes(), secretKey.getBytes());
	}
	
	/**
	 * Computes the seal
	 * @param bytes the bytes to seal
	 * @param secretKey the bytes of secret key
	 * @return hex representation of the seal, two chars per byte.
	 * @throws Exception
	 */
	public static String hmacSHA256Seal(byte[] bytes, byte[] secretKey) throws Exception {
		Mac hmac = Mac.getInstance("HmacSHA256");
		hmac.init(new SecretKeySpec(secretKey, "HmacSHA256"));
		return StringUtils.convertToHex(hmac.doFinal(bytes));
	}
	
	/**
	 * Computes the seal as base64
	 * 
	 * @param Data      the data to seal
	 * @param secretKey the secret key
	 * @return hex representation of the seal, two chars per byte.
	 */
	public static String hmacSHA256Base64Seal(String data, String secretKey) throws Exception {
		return hmacSHA256Base64Seal(data.getBytes(), secretKey.getBytes());
	}
	
	/**
	 * Computes the seal as base64
	 * 
	 * @param Data      the data to seal
	 * @param secretKey the secret key
	 * @return hex representation of the seal, two chars per byte.
	 */
	public static String hmacSHA256Base64Seal(byte[] bytes, byte[] secretKey) throws Exception {
		Mac hmac = Mac.getInstance("HmacSHA256");
		hmac.init(new SecretKeySpec(secretKey, "HmacSHA256"));
		return Base64.getEncoder().encodeToString(hmac.doFinal(bytes));
	}

	/**
	 * Computes the seal
	 * 
	 * @param Data      the data to seal
	 * @param secretKey the secret key
	 * @return hex representation of the seal, two chars per byte.
	 */
	public static String hmacSHA1Seal(String data, String secretKey) throws Exception {
		return hmacSHA1Seal(data.getBytes(), secretKey.getBytes());
	}
	
	/**
	 * Computes the seal
	 * @param bytes the bytes to seal
	 * @param secretKey the bytes of secret key
	 * @return hex representation of the seal, two chars per byte.
	 * @throws Exception
	 */
	public static String hmacSHA1Seal(byte[] bytes, byte[] secretKey) throws Exception {
		Mac hmac = Mac.getInstance("HmacSHA1");
		hmac.init(new SecretKeySpec(secretKey, "HmacSHA1"));
		return StringUtils.convertToHex(hmac.doFinal(bytes));
	}
	
	/**
	 * Computes the seal as base64
	 * 
	 * @param Data      the data to seal
	 * @param secretKey the secret key
	 * @return hex representation of the seal, two chars per byte.
	 */
	public static String hmacSHA1Base64Seal(String data, String secretKey) throws Exception {
		return hmacSHA1Base64Seal(data.getBytes(), secretKey.getBytes());
	}
	
	/**
	 * Computes the seal as base64
	 * 
	 * @param Data      the data to seal
	 * @param secretKey the secret key
	 * @return hex representation of the seal, two chars per byte.
	 */
	public static String hmacSHA1Base64Seal(byte[] bytes, byte[] secretKey) throws Exception {
		Mac hmac = Mac.getInstance("HmacSHA1");
		hmac.init(new SecretKeySpec(secretKey, "HmacSHA1"));
		return Base64.getEncoder().encodeToString(hmac.doFinal(bytes));
	}

	
	/**
	 * ECB cipher mode
	 * @param Data      the data to encrypt
	 * @param secretKey the secret key 
	 * @return hex representation of the encrypted data, two chars per byte.
	 */
	public static String blowfishEncrypt(String data, String secretKey) throws Exception {
		return StringUtils.convertToHex(blowfishEncrypt(data.getBytes(), secretKey.getBytes()));
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      the data to encrypt
	 * @param secretKey the secret key 
	 * @return base64 representation of the encrypted data, two chars per byte.
	 */
	public static String blowfishEncryptBase64(String data, String secretKey) throws Exception {
		return Base64.getEncoder().encodeToString(blowfishEncrypt(data.getBytes(), secretKey.getBytes()));
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      the data to encrypt
	 * @param secretKey the secret key 
	 * @return the encrypted data
	 */
	public static byte[] blowfishEncrypt(byte[] data, byte[] secretKey) throws Exception {
		Cipher cipher = Cipher.getInstance("Blowfish");
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secretKey, "Blowfish"));
		return cipher.doFinal(data);
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      the encrypted data
	 * @param secretKey the secret key 
	 * @return the decrypted data
	 */
	public static byte[] blowfishDecrypt(byte[] data, byte[] secretKey) throws Exception {
		Cipher cipher = Cipher.getInstance("Blowfish");
		cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(secretKey, "Blowfish"));
		return cipher.doFinal(data);
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      hex representation of the encrypted data
	 * @param secretKey the secret key 
	 * @return the decrypted data
	 */
	public static byte[] blowfishDecrypt(String data, String secretKey) throws Exception {
		return blowfishDecrypt(StringUtils.hexStringToByteArray(data), secretKey.getBytes());
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      base64 representation of the encrypted data
	 * @param secretKey the secret key 
	 * @return the decrypted data
	 */
	public static byte[] blowfishDecryptBase64(String data, String secretKey) throws Exception {
		return blowfishDecrypt(Base64.getDecoder().decode(data), secretKey.getBytes());
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      hex representation of the encrypted data
	 * @param secretKey the secret key 
	 * @return the decrypted data
	 */
	public static String blowfishDecryptToString(String data, String secretKey) throws Exception {
		return new String(blowfishDecrypt(data, secretKey));
	}
	
	/**
	 * ECB cipher mode
	 * 
	 * @param Data      base64 representation of the encrypted data
	 * @param secretKey the secret key 
	 * @return the decrypted data
	 */
	public static String blowfishDecryptBase64ToString(String data, String secretKey) throws Exception {
		return new String(blowfishDecryptBase64(data, secretKey));
	}

	/**
	 * @param keysize
	 * @return
	 * @throws NoSuchAlgorithmException 
	 */
	public static KeyPair rsaKeyPairGenerator(int keysize) throws NoSuchAlgorithmException {
		KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA");
        generator.initialize(keysize); 
        KeyPair keypair = generator.generateKeyPair();
        return keypair;
	}
	
	/**
	 * @param keysize
	 * @return
	 * @throws NoSuchAlgorithmException
	 */
	public static Pair<String, String> rsaKeyPairGeneratorMimeEncoded(int keysize) throws NoSuchAlgorithmException {
        KeyPair keypair = rsaKeyPairGenerator(keysize);
        
        String privateKey = new StringBuilder(String.format("-----BEGIN RSA-%d PRIVATE KEY-----", keysize)).append("\n")
    			.append(Base64.getMimeEncoder().encodeToString(keypair.getPrivate().getEncoded())).append("\n")
    			.append(String.format("-----END RSA-%d PRIVATE KEY-----", keysize)).append("\n")
    			.toString();
        
        String publicKey = new StringBuilder(String.format("-----BEGIN RSA-%d PUBLIC KEY-----", keysize)).append("\n")
    			.append(Base64.getMimeEncoder().encodeToString(keypair.getPublic().getEncoded())).append("\n")
    			.append(String.format("-----END RSA-%d PUBLIC KEY-----", keysize)).append("\n")
    			.toString();
        
        Pair<String, String> pair = Pair.of(privateKey, publicKey);
        return pair;
	}

	/**
	 * @param file
	 * @return
	 * @throws Exception
	 */
	public static RSAPublicKey rsaReadPublicKey(File file) throws Exception {
		String pem = new String(Files.readAllBytes(file.toPath()));
		return rsaReadPublicKey(pem);	
	}

	/**
	 * @param pem
	 * @return
	 * @throws Exception
	 */
	public static RSAPublicKey rsaReadPublicKey(String pem) throws Exception {
		
		byte[] bytes = Base64.getDecoder().decode(pem
			    .replaceAll("-.+-", "")
			    .replaceAll("\\r|\\n", ""));
		
		KeyFactory factory = KeyFactory.getInstance("RSA");
		X509EncodedKeySpec keySpec = new X509EncodedKeySpec(bytes);
		RSAPublicKey key = (RSAPublicKey) factory.generatePublic(keySpec);
		return key;
		
	}

	/**
	 * @param file
	 * @return
	 * @throws Exception
	 */
	public static RSAPrivateKey rsaReadPrivateKey(File file) throws Exception {
		String pem = new String(Files.readAllBytes(file.toPath()));
		return rsaReadPrivateKey(pem);	
	}
	
	/**
	 * @param pem
	 * @return
	 * @throws Exception
	 */
	public static RSAPrivateKey rsaReadPrivateKey(String pem) throws Exception {
		
		byte[] bytes = Base64.getDecoder().decode(pem
			    .replaceAll("-.+-", "")
			    .replaceAll("\\r|\\n", ""));
		
		KeyFactory factory = KeyFactory.getInstance("RSA");
		PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(bytes);
		RSAPrivateKey key = (RSAPrivateKey) factory.generatePrivate(keySpec);
		return key;
		
	}

	/**
	 * @param key
	 * @param content
	 * @return
	 * @throws NoSuchAlgorithmException 
	 * @throws InvalidKeyException 
	 * @throws SignatureException 
	 * @throws Exception
	 */
	public static String rsaSignatureSha256(RSAPrivateKey key, String content) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
		return rsaSignatureSha256(key, content.getBytes());
	}

	/**
	 * @param key
	 * @param bytes
	 * @return
	 * @throws NoSuchAlgorithmException 
	 * @throws InvalidKeyException 
	 * @throws SignatureException 
	 * @throws Exception
	 */
	public static String rsaSignatureSha256(RSAPrivateKey key, byte[] bytes) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
		
		Signature tool = Signature.getInstance("SHA256withRSA");
		tool.initSign(key);
		tool.update(bytes);
		
		byte[] signature = tool.sign();
		return Base64.getEncoder().encodeToString(signature);
		
	}

	/**
	 * @param key
	 * @param bytes
	 * @param signature
	 * @return
	 * @throws NoSuchAlgorithmException
	 * @throws InvalidKeyException
	 * @throws SignatureException
	 */
	public static boolean rsaVerifySignatureSha256(RSAPublicKey key, String content, String signature) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
		return rsaVerifySignatureSha256(key, content.getBytes(), signature);
	}

	/**
	 * @param key
	 * @param bytes
	 * @param signature
	 * @return
	 * @throws NoSuchAlgorithmException
	 * @throws InvalidKeyException
	 * @throws SignatureException
	 */
	public static boolean rsaVerifySignatureSha256(RSAPublicKey key, byte[] bytes, String signature) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
		
		Signature tool = Signature.getInstance("SHA256withRSA");
		tool.initVerify(key);
		tool.update(bytes);

		return tool.verify(Base64.getDecoder().decode(signature));
		
	}
	
}


