/**
 * 
 */
package com.helios.etl.outer.utils;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class ValuesFindReplaceMapper implements FindReplaceMapper {

	/**
	 * 
	 */
	protected Map<String, String> values;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected String defaultValue = "";

	/**
	 * 
	 */
	@Getter
	@Setter
	protected boolean defaultValueIsKey = false;

	/**
	 */
	public ValuesFindReplaceMapper() {
		this.values = new HashMap<>();
	}
	
	/**
	 * @param defaultValue
	 */
	public ValuesFindReplaceMapper(String defaultValue) {
		this.values = new HashMap<>();
		this.defaultValue = defaultValue;
	}

	/**
	 * @param isKey
	 */
	public ValuesFindReplaceMapper(boolean isKey) {
		this.values = new HashMap<>();
		this.defaultValueIsKey = isKey;
	}
	
	/**
	 * @param values
	 */
	public ValuesFindReplaceMapper(Map<String, String> values) {
		this.values = values;
	}
	
	/**
	 * @param values
	 * @param defaultValue
	 */
	public ValuesFindReplaceMapper(Map<String, String> values, String defaultValue) {
		this.values = values;
		this.defaultValue = defaultValue;
	}

	/**
	 * @param key
	 * @return
	 */
	protected String defaultValue(String key) {
		if(defaultValueIsKey) {
			return "{" + key + "}";
		} else {
			return defaultValue;
		}
	}
	
	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.FindReplaceMapper#map(java.lang.String)
	 */
	@Override
	public String map(String key) {
		if(values.containsKey(key)) {
			return values.get(key);
		} else {
			return defaultValue(key);
		}
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.FindReplaceMapper#hasMap(java.lang.String)
	 */
	@Override
	public boolean hasMap(String key) {
		return values.containsKey(key);
	}
	
	/**
	 * @param key
	 * @return
	 */
	public boolean containsKey(Object key) {
		return values.containsKey(key);
	}

	/**
	 * @param value
	 * @return
	 */
	public boolean containsValue(Object value) {
		return values.containsValue(value);
	}

	/**
	 * @param key
	 * @return
	 */
	public String getValue(Object key) {
		return values.get(key);
	}

	/**
	 * @param key
	 * @param value
	 * @return
	 */
	public String putValue(String key, String value) {
		return values.put(key, value);
	}

	/**
	 * @param key
	 * @return
	 */
	public String removeValue(Object key) {
		return values.remove(key);
	}

	/**
	 * @param key
	 * @param value
	 * @return
	 */
	public String replaceValue(String key, String value) {
		return values.replace(key, value);
	}

}


