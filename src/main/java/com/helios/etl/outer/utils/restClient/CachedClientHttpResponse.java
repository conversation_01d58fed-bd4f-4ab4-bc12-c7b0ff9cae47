/**
 * 
 */
package com.helios.etl.outer.utils.restClient;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpResponse;

/**
 * <AUTHOR>
 *
 */
public class CachedClientHttpResponse implements ClientHttpResponse {

	/**
	 * 
	 */
	ClientHttpResponse response;
	
	/**
	 * 
	 */
	byte[] body = new byte[]{};
	
	/**
	 * @param response
	 */
	public CachedClientHttpResponse(ClientHttpResponse response) {
		super();
		this.response = response;
		try {
			body = IOUtils.toByteArray(response.getBody());
		} catch (IOException e) {
			 body = new byte[]{};
		}
	}

	/* (non-Javadoc)
	 * @see org.springframework.http.HttpInputMessage#getBody()
	 */
	@Override
	public InputStream getBody() throws IOException {
		return new ByteArrayInputStream(body);
	}

	/* (non-Javadoc)
	 * @see org.springframework.http.HttpMessage#getHeaders()
	 */
	@Override
	public HttpHeaders getHeaders() {
		return response.getHeaders();
	}

	/* (non-Javadoc)
	 * @see org.springframework.http.client.ClientHttpResponse#getStatusCode()
	 */
	@Override
	public HttpStatusCode getStatusCode() throws IOException {
		return response.getStatusCode();
	}

	/**
	 * @return
	 * @throws IOException
	 */
	public int getStatusInt() throws IOException {
		return response.getStatusCode().value();
	}

	/* (non-Javadoc)
	 * @see org.springframework.http.client.ClientHttpResponse#getStatusText()
	 */
	@Override
	public String getStatusText() throws IOException {
		return response.getStatusText();
	}

	/* (non-Javadoc)
	 * @see org.springframework.http.client.ClientHttpResponse#close()
	 */
	@Override
	public void close() {
		response.close();
	}

}


