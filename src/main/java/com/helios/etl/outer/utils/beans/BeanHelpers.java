/**
 * 
 */
package com.helios.etl.outer.utils.beans;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Optional;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 *
 */
public abstract class BeanHelpers extends BeanUtils {

	/**
	 * Copy the property values of the given source bean into the given target bean,
	 * ignoring fields annotated with {@link BeanUpdateIgnore}
	 * @param source the source bean
	 * @param target the target bean
	 * @throws BeansException if the copying failed
	 */
	public static void copyFields(Object source, Object target, String... ignoreProperties) throws BeansException {
		
		Class<?> clazz = target.getClass();
		Field[] fields = clazz.getDeclaredFields();
		
		String[] excludedProperties = Arrays.stream(fields).map(Field::getName).filter(name -> {
			try {
				return clazz.getDeclaredField(name).isAnnotationPresent(BeanUpdateIgnore.class);
			} catch (Exception e) {
				throw new FieldAccessException(e);
			} 
		}).toArray(String[]::new);
    	
    	BeanUtils.copyProperties(source, target, excludedProperties);
    	
	}
	
	/**
	 * @param from
	 * @param to
	 * @param field
	 * @param finder
	 * @param nullify
	 */
	public static void quickResolveRelation(Object from, String field, BeanFinder finder, boolean nullify) {
		quickResolveRelation(from, from, field, finder, nullify);
	}
	
	/**
	 * Methode gÃ©nÃ©rique pour lier une entitÃ© managÃ©e dÃ©finie dans from Ã  to
	 * @param from
	 * @param to
	 * @param field
	 * @param finder
	 * @param nullify
	 */
	public static void quickResolveRelation(Object from, Object to, String field, BeanFinder finder, boolean nullify) {
		
		String capitalized = field.substring(0, 1).toUpperCase() + field.substring(1);
		
		Method getter = ReflectionUtils.findMethod(from.getClass(), "get" + capitalized);
		Method setter = ReflectionUtils.findMethod(to.getClass(), "set" + capitalized, (Class<?>[]) null);

		long oid = 0;
		
		if(getter != null && setter != null) {
			
			try {
				Object property = getter.invoke(from);
				
				if(property != null) {
					
					Method oidGetter = ReflectionUtils.findMethod(property.getClass(), "getOid");
					oid = (long)oidGetter.invoke(property);
					
					property = null;
					
					if(oid > 0) {
						Optional<?> _option = finder.find(oid);
						
						if(_option.isPresent()) {
							property = _option.get();
						}
					}
					
				}
				
				if(property != null) {
					setter.invoke(to, property);
				} else if(nullify){
					Object nullobj = null;
					setter.invoke(to, nullobj);
				}
				
			} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
				throw new RuntimeException(e.getMessage());
			}
			
		} else {
			throw new RuntimeException(String.format("Unable to quick resolve relation %s, getter or setter is null", field));
		}
		
	}
	
}


