/**
 * 
 */
package com.helios.etl.outer.utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 *
 */
public class DataTypeConverter {

	/**
	 * @param value
	 * @return
	 */
	static public String asString(Object value) {

		if(value instanceof String) {
			return (String)value;
			
		} else {
			return value.toString();
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public byte[] asBytes(Object value) {

		if(value instanceof String) {
			return ((String)value).getBytes();
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to byte[]", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public boolean asBoolean(Object value) {

		if(value instanceof Boolean) {
			return (boolean)value;
			
		} else if(value instanceof Integer) {
			return (int)value <= 0 ? false : true;
			
		} else if(value instanceof Long) {
			return (long)value <= 0l ? false : true;
			
		} else if(value instanceof String) {
			
			String val = (String)value;
			if(val.length() == 4 && val.equalsIgnoreCase("true")) {
				return true;
				
			} else if(val.length() == 5 && val.equalsIgnoreCase("false")) {
				return false;
				
			} else {
				return Integer.parseInt(val) <= 0 ? false : true;
			}
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to boolean", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public float asFloat(Object value) {

		if(value instanceof Float) {
			return (float)value;
				
		} else if(value instanceof Double) {
			return ((Double)value).floatValue();
			
		} else if(value instanceof BigDecimal) {
			return ((BigDecimal)value).floatValue();
			
		} else if(value instanceof Integer) {
			return ((Integer)value).floatValue();
			
		} else if(value instanceof Long) {
			return ((Long)value).floatValue();
			
		} else if(value instanceof BigInteger) {
			return ((BigInteger)value).floatValue();
			
		} else if(value instanceof String) {
			return Float.parseFloat((String)value);
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to float", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public BigDecimal asBigDecimal(Object value) {

		if(value instanceof BigDecimal) {
			return (BigDecimal) value;
			
		} else if(value instanceof Integer) {
			return BigDecimal.valueOf(((Integer)value));
			
		} else if(value instanceof Long) {
			return BigDecimal.valueOf(((Long)value).floatValue());
			
		} else if(value instanceof Float) {
			return (BigDecimal.valueOf((float)value));
			
		} else if(value instanceof Double) {
			return BigDecimal.valueOf(((Double)value).floatValue());
			
		} else if(value instanceof String) {
			if(((String)value).isEmpty()) {
				return BigDecimal.ZERO;
			} else {
				return BigDecimal.valueOf(Float.parseFloat(((String)value)));	
			}
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to BigDecimal", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public double asDouble(Object value) {

		if(value instanceof Double) {
			return (double)value;
			
		} else if(value instanceof Float) {
			return ((Float)value).doubleValue();
			
		} else if(value instanceof BigDecimal) {
			return ((BigDecimal)value).doubleValue();
			
		} else if(value instanceof Integer) {
			return ((Integer)value).doubleValue();
			
		} else if(value instanceof Long) {
			return ((Long)value).doubleValue();
			
		} else if(value instanceof BigInteger) {
			return ((BigInteger)value).doubleValue();
			
		} else if(value instanceof String) {
			return Float.parseFloat((String)value);
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to float", value.getClass()));
		}
		
	}
	
	/**
	 * @param key
	 * @return
	 */
	static public int asInteger(Object value) {
		
		if(value instanceof Integer) {
			return (int)value;
			
		} else if(value instanceof Long) {
			return ((Long)value).intValue();
			
		} else if(value instanceof BigInteger) {
			return((BigInteger) value).intValue();
			
		} else if(value instanceof Float) {
			return ((Float)value).intValue();
			
		} else if(value instanceof Double) {
			return ((Double)value).intValue();
			
		} else if(value instanceof String) {
			return Integer.parseInt((String)value);

		} else if(value instanceof BigDecimal) {
			return((BigDecimal) value).intValue();
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to integer", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public long asLong(Object value) {

		if(value instanceof Integer) {
			return (int)value;
			
		} else if(value instanceof Long) {
			return ((Long)value).longValue();
			
		} else if(value instanceof BigInteger) {
			return((BigInteger) value).longValue();
			
		} else if(value instanceof BigDecimal) {
			return((BigDecimal) value).longValue();
			
		} else if(value instanceof Float) {
			return ((Float)value).longValue();
			
		} else if(value instanceof Double) {
			return ((Double)value).longValue();
			
		} else if(value instanceof String) {
			return Long.parseLong((String)value);
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to long", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public LocalDate asLocalDate(Object value) {
		
		if(value instanceof String) {
			return LocalDate.parse((String)value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			
		} else if(value instanceof java.sql.Date) {
			return ((java.sql.Date)value).toLocalDate();
			
		} else if(value instanceof java.util.Date) {
			return ((java.util.Date)value).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to LocalDate", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public LocalDateTime asLocalDateTime(Object value) {	
		
		if(value instanceof LocalDateTime) {
			return (LocalDateTime) value;
			
		} else if(value instanceof java.util.Date) {
			return ((java.util.Date)value).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
			
		} else if(value instanceof java.sql.Date) {
			return (LocalDateTime.of(((java.sql.Date)value).toLocalDate(), LocalTime.of(0,0,0,0)));

		} else if(value instanceof java.sql.Timestamp) {
			return ((java.sql.Timestamp) value).toLocalDateTime();		

		} else if(value instanceof String) {
			return LocalDateTime.parse((String)value, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to LocalDateTime", value.getClass()));
		}
		
	}
	
	/**
	 * @param value
	 * @return
	 */
	static public LocalTime asLocalTime(Object value) {
		
		if(value instanceof LocalTime) {
			return (LocalTime) value;
			
		} else if(value instanceof java.sql.Time) {
			return ((java.sql.Time)value).toLocalTime();
			
		} else if(value instanceof java.util.Date) {
			return ((java.util.Date)value).toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
			
		} else if(value instanceof LocalDateTime) {
			return ((LocalDateTime)value).toLocalTime();
			
		} else if(value instanceof String) {
			return LocalTime.parse((String)value, DateTimeFormatter.ofPattern("hh:mm:ss"));
			
		} else {
			throw new RuntimeException(String.format("Unable to convert %s to LocalTime", value.getClass()));
		}
		
	}
}


