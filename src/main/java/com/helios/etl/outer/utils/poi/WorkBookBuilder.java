/**
 * 
 */
package com.helios.etl.outer.utils.poi;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import lombok.Getter;
import lombok.Setter;

/**
 * 
 * http://poi.apache.org/apidocs/dev/org/apache/poi/ss/usermodel/BuiltinFormats.html
 * 
 * <AUTHOR>
 *
 */
public class WorkBookBuilder {

	/**
	 * 
	 */
	@Getter
	@Setter
	protected SXSSFWorkbook book;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected List<SXSSFSheet> sheets = new ArrayList<>();

	/**
	 * 
	 */
	@Getter
	@Setter
	protected Map<String, CellStyle> styles = new HashMap<>();

	/**
	 * 
	 */
	@Getter
	@Setter
	protected boolean styleAuto = true;
	
	
	/**
	 * 
	 */
	public WorkBookBuilder() {
		
	}
	
	/**
	 * @return
	 */
	public WorkBookBuilder init() {
		book = new SXSSFWorkbook();
		
		sheets.clear();
		styles.clear();
		
		CellStyle style = null;
		
		// -----------------------------------------------------------------
		Font font = book.createFont();
		font.setBold(true);
		
		style = book.createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setFont(font);
		style.setDataFormat(createFormat("#,##"));
		styles.put("header", style);

		// -----------------------------------------------------------------
		style = book.createCellStyle();
		style.setDataFormat(createFormat("#,##"));
		styles.put("integer", style);

		// -----------------------------------------------------------------
		style = book.createCellStyle();
		style.setDataFormat(createFormat("#,##0.00"));
		styles.put("float", style);

		// -----------------------------------------------------------------
		style = book.createCellStyle();
		style.setDataFormat(createFormat("m/d/yy"));
		styles.put("date", style);

		// -----------------------------------------------------------------
		style = book.createCellStyle();
		style.setDataFormat(createFormat("m/d/yy h:mm"));
		styles.put("datetime", style);

		// -----------------------------------------------------------------
		style = book.createCellStyle();
		style.setDataFormat(createFormat("h:mm"));
		styles.put("time", style);
		
		// -----------------------------------------------------------------
		style = book.createCellStyle();
		style.setWrapText(true);
		styles.put("autowrap", style);
		
		return this;
	}
	
	/**
	 * @param sheetname
	 * @return
	 */
	public WorkBookBuilder createSheet(String sheetname) {
		SXSSFSheet sheet = book.createSheet(sheetname);
		sheets.add(sheet);
		book.setActiveSheet(sheets.size() -1);
		return this;
	}
	
	/**
	 * @param sheetname
	 * @return
	 */
	public WorkBookBuilder selectSheet(String sheetname) {
		
		for(int i = 0; i < sheets.size(); i++) {
			SXSSFSheet sheet = sheets.get(i);
			if(sheet.getSheetName().equals(sheetname)) {
				book.setActiveSheet(i);
			}
		}

		return this;
	}
	
	/**
	 * @param index
	 * @return
	 */
	public WorkBookBuilder selectSheet(int index) {
		book.setActiveSheet(index);
		return this;
	}
	
	/**
	 * @param values
	 * @return
	 */
	public WorkBookBuilder addHeader(String... values) {
		SXSSFSheet sheet = sheets.get(book.getActiveSheetIndex());

		SXSSFRow row = sheet.createRow(0);
		
		for(int i = 0; i < values.length; i++) {
			SXSSFCell cell = row.createCell(i);
			assignvalue(cell, values[i]);
			cellStyle(cell, "header");
		}
		
		return this;
	}
	
	/**
	 * @param values
	 * @return
	 */
	public WorkBookBuilder addRow(Object... values) {
		SXSSFSheet sheet = sheets.get(book.getActiveSheetIndex());

		int rowIndex = sheet.getPhysicalNumberOfRows();
		SXSSFRow row = sheet.createRow(rowIndex);
		
		for(int i = 0; i < values.length; i++) {
			SXSSFCell cell = row.createCell(i);
			assignvalue(cell, values[i]);
		}
		
		return this;
	}
	
	/**
	 * @param values
	 * @return
	 */
	public WorkBookBuilder addRowWithAutoWrap(Object... values) {
		SXSSFSheet sheet = sheets.get(book.getActiveSheetIndex());

		int rowIndex = sheet.getPhysicalNumberOfRows();
		SXSSFRow row = sheet.createRow(rowIndex);
		
		for(int i = 0; i < values.length; i++) {
			SXSSFCell cell = row.createCell(i);
			assignvalue(cell, values[i]);
			cellStyle(cell, "autowrap");
		}
		
		return this;
	}
	
	/**
	 * @param colunmsCount
	 * @return
	 */
	public WorkBookBuilder autosizeSheets(int colunmsCount) {
		
		for(int i = 0; i < book.getNumberOfSheets(); i++) {
			SXSSFSheet sheet = sheets.get(i);
			sheet.trackAllColumnsForAutoSizing();

			for(int j = 0; j < colunmsCount; j++) {
				sheet.autoSizeColumn(j);
			}
		}
		
		return this;
	}

	/**
	 * @param pathname
	 * @throws Exception
	 */
	public void writeFile(String pathname) throws Exception {
		OutputStream fos = new FileOutputStream(pathname);
		book.write(fos);
		fos.close();
	}

	/**
	 * @param file
	 * @throws Exception
	 */
	public void writeFile(File file) throws Exception {
		writeFile(file.getAbsolutePath());
	}

	/**
	 * @param cell
	 * @param value
	 */
	protected void assignvalue(SXSSFCell cell, Object value) {
		
		if(value == null) {
			value = new String("");
		}
		
		if(!checkTypeOfValue(value)) {
			throw new RuntimeException("Cannot add value of type " + value.getClass().getSimpleName());
		}
		
		if(value.getClass().isAssignableFrom(Boolean.class)) {
			cell.setCellValue((Boolean)value);
			
		} else if(value.getClass().isAssignableFrom(Float.class)) {
			cell.setCellValue(((Float)value).doubleValue());
			if(styleAuto) {
				cellStyle(cell, "float");
			}
			
		} else if(value.getClass().isAssignableFrom(Double.class)) {
			cell.setCellValue((Double)value);
			if(styleAuto) {
				cellStyle(cell, "float");
			}
			
		} else if(value.getClass().isAssignableFrom(Long.class)) {
			cell.setCellValue((Long)value);
			if(styleAuto) {
				cellStyle(cell, "integer");
			}
			
		} else if(value.getClass().isAssignableFrom(Integer.class)) {
			cell.setCellValue(((Integer)value).longValue());
			if(styleAuto) {
				cellStyle(cell, "integer");
			}
			
		} else if(value.getClass().isAssignableFrom(BigInteger.class)) {
			cell.setCellValue(((BigInteger)value).longValue());
			if(styleAuto) {
				cellStyle(cell, "integer");
			}
			
		} else if(value.getClass().isAssignableFrom(BigDecimal.class)) {
			cell.setCellValue(((BigDecimal)value).doubleValue());
			if(styleAuto) {
				cellStyle(cell, "float");
			}
			
		} else if(value.getClass().isAssignableFrom(LocalDate.class)) {
			cell.setCellValue((LocalDate)value);
			if(styleAuto) {
				cellStyle(cell, "date");
			}
			
		} else if(value.getClass().isAssignableFrom(LocalDateTime.class)) {
			cell.setCellValue((LocalDateTime)value);
			if(styleAuto) {
				cellStyle(cell, "datetime");
			}
			
		} else if(value.getClass().isAssignableFrom(LocalTime.class)) {
			cell.setCellValue(((LocalTime)value).atDate(LocalDate.now()));
			if(styleAuto) {
				cellStyle(cell, "time");
			}
			
		} else if(value.getClass().isAssignableFrom(Date.class)) {
			cell.setCellValue((Date)value);
			if(styleAuto) {
				cellStyle(cell, "date");
			}
			
		} else if(value.getClass().isAssignableFrom(RichTextString.class)) {
			cell.setCellValue((RichTextString)value);
			
		} else if(value.getClass().isAssignableFrom(String.class)) {
			cell.setCellValue((String)value);
			
		}
		
	}

	/**
	 * @param cell
	 */
	public void cellStyle(SXSSFCell cell, String key) {
		cell.setCellStyle(styles.get(key));
	}
	
	/**
	 * @param format
	 * @return
	 */
	protected short createFormat(String format) {
		CreationHelper createHelper = book.getCreationHelper();
		return createHelper.createDataFormat().getFormat(format);
	}
	
	/**
	 * @param value
	 * @return
	 */
	protected boolean checkTypeOfValue(Object value) {
		
		if(value.getClass().isAssignableFrom(Boolean.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(Double.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(Float.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(Integer.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(Long.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(BigInteger.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(BigDecimal.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(Date.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(LocalDate.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(LocalDateTime.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(LocalTime.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(String.class)) {
			return true;
			
		} else if(value.getClass().isAssignableFrom(RichTextString.class)) {
			return true;
			
		} else {
			return false;
		}
	}
}


