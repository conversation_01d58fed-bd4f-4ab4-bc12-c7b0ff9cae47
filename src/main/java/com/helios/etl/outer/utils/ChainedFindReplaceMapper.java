/**
 * 
 */
package com.helios.etl.outer.utils;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class ChainedFindReplaceMapper implements FindReplaceMapper {

	/**
	 * 
	 */
	protected List<FindReplaceMapper> mappers = new ArrayList<>();

	/**
	 * 
	 */
	@Getter
	@Setter
	protected String defaultValue = "";

	/**
	 * 
	 */
	@Getter
	@Setter
	protected boolean defaultValueIsKey = false;

	/**
	 */
	public ChainedFindReplaceMapper() {
	}
	
	/**
	 * @param defaultValue
	 */
	public ChainedFindReplaceMapper(String defaultValue) {
		this.defaultValue = defaultValue;
	}

	/**
	 * @param defaultValue
	 * @param isKey
	 */
	public ChainedFindReplaceMapper(String defaultValue, boolean isKey) {
		this.defaultValue = defaultValue;
		this.defaultValueIsKey = isKey;
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.FindReplaceMapper#map(java.lang.String)
	 */
	@Override
	public String map(String key) {
		
		for(FindReplaceMapper mapper : mappers) {
			if(mapper.hasMap(key)){
				return mapper.map(key);
			}
		}
		
		return defaultValue(key);
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.FindReplaceMapper#hasMap(java.lang.String)
	 */
	@Override
	public boolean hasMap(String key) {
		for(FindReplaceMapper mapper : mappers) {
			if(mapper.hasMap(key)){
				return true;
			}
		}
		
		return false;
	}

	/**
	 * @param key
	 * @return
	 */
	protected String defaultValue(String key) {
		if(defaultValueIsKey) {
			return "{" + key + "}";
		} else {
			return defaultValue;
		}
	}

	/**
	 * @return
	 */
	public int size() {
		return mappers.size();
	}

	/**
	 * @param o
	 * @return
	 */
	public boolean contains(Object o) {
		return mappers.contains(o);
	}

	/**
	 * @param e
	 * @return
	 */
	public boolean add(FindReplaceMapper e) {
		return mappers.add(e);
	}

	/**
	 * @param o
	 * @return
	 */
	public boolean remove(Object o) {
		return mappers.remove(o);
	}

	/**
	 * 
	 */
	public void clear() {
		mappers.clear();
	}

}


