/**
 * 
 */
package com.helios.etl.outer.utils.regex;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
public class ChainedPatternMatcher {

	/**
	 * 
	 */
	@Getter
	protected List<PatternMatcher> matchers;

	/**
	 * 
	 */
	@Getter
	protected List<PatternMatcherConverter> converters;

	/**
	 * 
	 */
	@Getter
	@Setter
	protected boolean matchWhenEmpty = false;
	
	/**
	 * 
	 */
	public ChainedPatternMatcher() {
		matchers = new ArrayList<>();
		converters = new ArrayList<>();
	}
	
	/**
	 * @param matchWhenEmpty
	 */
	public ChainedPatternMatcher(boolean matchWhenEmpty) {
		this();
		this.matchWhenEmpty = matchWhenEmpty;
	}

	/**
	 * @param converter
	 */
	public void addConverter(PatternMatcherConverter converter) {
		converters.add(converter);
	}

	/**
	 */
	public void clear() {
		matchers.clear();
	}
	
	/**
	 * @param rule
	 */
	public void compile(String... rule) {
		compile(Arrays.asList(rule));
	}
	
	/**
	 * @param rule
	 */
	public void compile(String rule) {
		compile(Arrays.asList(rule.split("\\R")));
	}
	
	/**
	 * @param rules
	 */
	public void compile(List<String> rules) {
		
		clear();
		
		if(rules == null || rules.size() == 0) {
			return;
		}
		
		for(String rule : rules) {
			
			if(rule == null || rule.length() == 0) {
				continue;
			}
			
			for(PatternMatcherConverter c : converters) {
				
				if(c.supports(rule)) {
					matchers.add(c.compile(rule));
					break;
				}
				
			}
		}
		
	}
	
	/**
	 * @return
	 */
	public List<String> getRules(){
		List<String> rules = new ArrayList<>();
		
		if(matchers != null && matchers.size() > 0){

			for(PatternMatcher matcher : matchers){
				rules.add(matcher.rule());
			}
			
		}
		
		return rules;
	}


	/**
	 * Tells whether or not this string matches the list of PatternMatcher
	 * 
	 * @param input
	 * @param defaultValue
	 * @return true for the first positive result, false otherwise; If no PatternMatcher exists in this chain,
	 * defaultMatchWhenEmpty is returned
	 */
	public boolean matches(String input, boolean defaultMatchWhenEmpty){

		if(matchers != null && matchers.size() > 0){

			for(PatternMatcher matcher : matchers){

				if(matcher.matches(input)){
					return true;
				}
				
			}

			return false;
			
		} else {
			return defaultMatchWhenEmpty;
		}
		
	}
	
	/**
	 * @param input
	 * @return
	 */
	public boolean matches(String input){
		return matches(input, matchWhenEmpty);
	}
	
	/**
	 * @param input
	 * @return
	 */
	public boolean noMatches(String input){
		return !matches(input, matchWhenEmpty);
	}
	
}


