package com.helios.etl.outer.utils.remote;

import java.io.File;
import java.io.IOException;

import lombok.Getter;
import lombok.Setter;

abstract public class RemoteOperations {

	/**
	 * 
	 */
	@Getter
	@Setter
	protected RemoteDock dock;

	/**
	 * @param dock
	 * @param filepath
	 * @param file
	 * @return
	 * @throws IOException 
	 * @throws RemoteDockException 
	 */
	public abstract void transferFile(String filepath, File file) throws IOException, RemoteDockException;

}

