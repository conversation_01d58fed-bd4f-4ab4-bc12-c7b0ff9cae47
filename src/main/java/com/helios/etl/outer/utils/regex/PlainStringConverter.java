/**
 * 
 */
package com.helios.etl.outer.utils.regex;

/**
 * <AUTHOR>
 *
 */
public class PlainStringConverter implements PatternMatcherConverter {

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.regex.PatternMatcherConverter#supports(java.lang.String)
	 */
	@Override
	public boolean supports(String rule) {
		return true;
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.regex.PatternMatcherConverter#compile(java.lang.String)
	 */
	@Override
	public PatternMatcher compile(String rule) {
		return new StringPatternMatcher(rule);
	}

}


