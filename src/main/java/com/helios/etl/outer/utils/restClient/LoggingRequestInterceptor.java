package com.helios.etl.outer.utils.restClient;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

/**
 * <AUTHOR>
 *
 */
public class LoggingRequestInterceptor implements ClientHttpRequestInterceptor {

	/**
	 * 
	 */
	final static Logger log = LoggerFactory.getLogger("com.helios.etl.outer.utils.restClient");

	/* (non-Javadoc)
	 * @see org.springframework.http.client.ClientHttpRequestInterceptor#intercept(org.springframework.http.HttpRequest, byte[], org.springframework.http.client.ClientHttpRequestExecution)
	 */
	@Override
	public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
		
		if(log.isDebugEnabled()) {
			traceRequest(request, body);
		}
		
		CachedClientHttpResponse response = new CachedClientHttpResponse(execution.execute(request, body));
		
		if(log.isDebugEnabled()) {
			traceResponse(response);
		}	
	
		return response;
	}

	/**
	 * @param request
	 * @param body
	 * @throws IOException
	 */
	private void traceRequest(HttpRequest request, byte[] body) throws IOException {

		log.debug("===========================REQUEST BEGIN================================================");
		log.debug("URI         : {}", request.getURI());
		log.debug("Method      : {}", request.getMethod());
		log.debug("Headers     : {}", request.getHeaders());
		log.debug("Request body: {}", new String(body, "UTF-8"));
		log.debug("==========================REQUEST END================================================");
		
	}

	/**
	 * @param response
	 * @throws IOException
	 */
	private void traceResponse(ClientHttpResponse response) throws IOException {
		String body = "Empty or null body";
		
		try {
			
			if(response.getBody() != null) {
				body = IOUtils.toString(response.getBody(), StandardCharsets.UTF_8);;
			}
			
		} catch (Exception e) {
			body = "Exception while reading body : " + e.getMessage();
		}
		
		log.debug("============================RESPONSE BEGIN==========================================");
		log.debug("Status code  : {}", response.getStatusCode());
		log.debug("Status text  : {}", response.getStatusText());
		log.debug("Headers      : {}", response.getHeaders());
		
		if(response.getHeaders() == null) {
			log.debug("Response body: Not printable --> no headers");
		} else if(response.getHeaders().getContentType() == null) {
			log.debug("Response body: Not printable --> no contentType");
		} else if(!isPrintable(response.getHeaders().getContentType())) {
			log.debug("Response body: MediaType '{}' is not printable", response.getHeaders().getContentType());
		} else {
			log.debug("Response body: {}", body);
		}
		
		log.debug("=======================RESPONSE END=================================================");
		
	}
	
	/**
	 * @param type
	 * @return
	 */
	private boolean isPrintable(MediaType type) {
		
		if(type == null) {
			return false;
		}
		
		return  type.equalsTypeAndSubtype(MediaType.APPLICATION_JSON)
			|| type.equalsTypeAndSubtype(MediaType.APPLICATION_PROBLEM_JSON)
			|| type.equalsTypeAndSubtype(MediaType.APPLICATION_RSS_XML)
			|| type.equalsTypeAndSubtype(MediaType.APPLICATION_ATOM_XML)
			|| type.equalsTypeAndSubtype(MediaType.APPLICATION_XML)
			|| type.equalsTypeAndSubtype(MediaType.APPLICATION_PROBLEM_XML)
			|| type.equalsTypeAndSubtype(MediaType.APPLICATION_XHTML_XML)
			|| type.equalsTypeAndSubtype(MediaType.TEXT_HTML)
			|| type.equalsTypeAndSubtype(MediaType.TEXT_MARKDOWN)
			|| type.equalsTypeAndSubtype(MediaType.TEXT_PLAIN)
			|| type.equalsTypeAndSubtype(MediaType.TEXT_XML)
			;
	}

}

