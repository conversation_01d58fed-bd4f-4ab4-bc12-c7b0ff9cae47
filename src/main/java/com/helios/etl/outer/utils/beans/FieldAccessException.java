/**
 * 
 */
package com.helios.etl.outer.utils.beans;

import java.beans.PropertyChangeEvent;

import org.springframework.beans.PropertyAccessException;


/**
 * <AUTHOR>
 *
 */
public class FieldAccessException extends PropertyAccessException {

	/**
	 * 
	 */
	private static final long serialVersionUID = -621052708913827514L;

	/**
	 * @param cause
	 */
	public FieldAccessException(Throwable cause) {
		this(cause.getMessage(), cause);
	}
	
	/**
	 * @param msg
	 * @param cause
	 */
	public FieldAccessException(String msg, Throwable cause) {
		super(msg, cause);
	}

	/**
	 * @param propertyChangeEvent
	 * @param msg
	 * @param cause
	 */
	public FieldAccessException(PropertyChangeEvent propertyChangeEvent, String msg, Throwable cause) {
		super(propertyChangeEvent, msg, cause);
	}

	/* (non-Javadoc)
	 * @see org.springframework.beans.PropertyAccessException#getErrorCode()
	 */
	@Override
	public String getErrorCode() {
		return null;
	}

}


