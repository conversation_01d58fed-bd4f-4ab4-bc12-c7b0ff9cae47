/**
 * 
 */
package com.helios.etl.outer.utils.net;

import org.apache.commons.net.util.SubnetUtils;
import org.apache.commons.net.util.SubnetUtils.SubnetInfo;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 *
 */
public class NetworkAddressValidator {
	
	/**
	 * @param ip
	 * @return
	 */
	public static SubnetInfo checkIp(String ip) {
		Assert.hasText(ip, "Parameter ip cannot be null or 0-length");
		
		if(ip.indexOf('/') == -1) {
			ip += "/32";
		};
		
		SubnetUtils subnet = new SubnetUtils(ip);
		subnet.setInclusiveHostCount(true);
		return subnet.getInfo();

	}
	
	/**
	 * @param range
	 * @param ip
	 * @return
	 */
	public static boolean checkInRange(String allowedRange, String ip) {
		Assert.hasText(allowedRange, "Parameter range cannot be null or 0-length");
		Assert.hasText(ip, "Parameter ip cannot be null or 0-length");
		
		if(allowedRange.equalsIgnoreCase("*") || allowedRange.equalsIgnoreCase("any")) {
			return true;
		}
		
		allowedRange = allowedRange.replace(';', ',');
		allowedRange = allowedRange.replace('|', ',');
		
		String[] ranges = allowedRange.split(",");
		for(String range : ranges) {
			range = range.trim();
			
			if(range.indexOf('/') == -1) {
				range += "/32";
			}
			
			SubnetUtils subnet = new SubnetUtils(range);
			subnet.setInclusiveHostCount(true);
			if(subnet.getInfo().isInRange(ip)) {
				return true;
			}
		}
		
		return false;
	}
}


