/**
 * 
 */
package com.helios.etl.outer.utils.restClient;

import java.io.IOException;
import java.net.URISyntaxException;

import org.apache.hc.core5.http.HttpHost;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


/**
 * <AUTHOR>
 *
 */
public class MediaClient {
	
	/**
	 * 
	 */
	private RestTemplate client;
	
	/**
	 * @param url
	 * @param user
	 * @param password
	 */
	public MediaClient(String url, String username, String password) throws LogicException {
		
		HttpHost host = null;
		try {
			HttpHost.create(url);
		} catch (URISyntaxException e) {
			throw new LogicException("JsonClient", e.getMessage());
		}
		
		client = new RestTemplate(new BufferingClientHttpRequestFactory(new HttpComponentsClientHttpRequestFactoryBasicAuth(host)));
		client.getInterceptors().add(new BasicAuthenticationInterceptor(username, password));
		client.getInterceptors().add(new LoggingRequestInterceptor());
		client.getMessageConverters().add(0, new ByteArrayHttpMessageConverter());
	}
	
	/**
	 * @param builder
	 * @return
	 * @throws IOException
	 */
	public byte[] get(UriComponentsBuilder builder) throws IOException {
		return client.getForObject(builder.build().toUri(), byte[].class);
	}
	
	/**
	 * @param builder
	 * @return
	 * @throws IOException
	 */
	public byte[] getForObject(UriComponentsBuilder builder) throws IOException {
		return client.getForObject(builder.build().toUri(), byte[].class);
	}
	
	/**
	 * @param builder
	 * @return
	 * @throws IOException
	 */
	public ResponseEntity<byte[]> getForEntity(UriComponentsBuilder builder) throws IOException {
		return client.getForEntity(builder.build().toUri(), byte[].class);
	}
	
}


