package com.helios.etl.helper;

public class DescriptionHelper {
    public static String GetTicketTypeDescription(String type)
    {
        type = type.toLowerCase();

        return switch (type) {
            case "support" -> "Mission de support ordinaire.";
            case "infogerant" -> "Mission d'infogérance: L'infogérance est la prise en charge contractuelle, d'une partie ou de la totalité des ressources informatiques d'une entreprise.";
            case "installation" -> "Mission d'installation, normalement lié à une commande.";
            case "suite-installation", "suite installation" -> "Mission en lien avec une précédente installation, ex: poursuite d'une installation existante ou support suite à l'installation.";
            case "astreinte" -> "Mission d'astreinte: service de permanence technique assurant une disponibilité en dehors des heures ouvrables.";
            case "avant-vente" -> "Mission avant-vente: activités de conseil et d'analyse technique préalables à une proposition commerciale.";
            case "commerce" -> "Mission commerciale: activités de prospection, négociation et développement commercial.";
            default -> "Type de mission non défini.";
        };
    }

    public static String GetPoleDescription(String pole) {
        pole = pole.toLowerCase();

        return switch (pole) {
            case "logiciel" -> "Pôle logiciel: En charge de la vente et du support des solutions logicielles (Sage100, EBP, etc.).";
            case "informatique" -> "Pôle informatique: En charge de la vente et du support des solutions informatiques (matériel, réseaux, etc.).";
            case "telecom" -> "Pôle télécom: En charge de la vente et du support des solutions de télécommunications (téléphonie, internet, etc.).";
            case "bureautique" -> "Pôle bureautique: En charge de la vente et du support des solutions bureautiques (impression, photocopie, etc.).";
            default -> "Pôle non défini.";
        };
    }
}
