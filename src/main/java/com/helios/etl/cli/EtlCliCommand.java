package com.helios.etl.cli;

/**
 * Data class representing a parsed CLI command for ETL operations
 */
public class EtlCliCommand {
    
    private String commandName;
    private String entityType;
    private Integer days;
    
    // Constructors
    public EtlCliCommand() {}
    
    public EtlCliCommand(String commandName) {
        this.commandName = commandName;
    }
    
    // Getters and Setters
    public String getCommandName() {
        return commandName;
    }
    
    public void setCommandName(String commandName) {
        this.commandName = commandName;
    }
    
    public String getEntityType() {
        return entityType;
    }
    
    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }
    
    public Integer getDays() {
        return days;
    }
    
    public void setDays(Integer days) {
        this.days = days;
    }
    
    // Helper methods
    public boolean hasEntityType() {
        return entityType != null && !entityType.trim().isEmpty();
    }
    
    public boolean hasDays() {
        return days != null && days > 0;
    }
    
    @Override
    public String toString() {
        return "EtlCliCommand{" +
                "commandName='" + commandName + '\'' +
                ", entityType='" + entityType + '\'' +
                ", days=" + days +
                '}';
    }
}
