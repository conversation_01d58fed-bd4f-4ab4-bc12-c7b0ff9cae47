package com.helios.etl.cli;

/**
 * Minimal no-op progress reporter kept for compatibility.
 * Not used by the current CLI runner.
 */
class EtlCliProgressReporter {
    public void startMonitoring() {}
    public void stopMonitoring() {}
    public void reportStep(String stepName, String description) {}
    public void reportError(String error) {}
    public void reportWarning(String warning) {}
    public void reportSuccess(String message) {}
}
