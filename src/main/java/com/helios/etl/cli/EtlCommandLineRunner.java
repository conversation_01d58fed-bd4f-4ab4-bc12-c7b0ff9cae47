package com.helios.etl.cli;

import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.services.HeliosFilesService;
import com.helios.etl.services.ProgressListener;
import com.helios.etl.services.Tranformer;
import com.helios.etl.services.CacheMemory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "com.helios.etl")
@EntityScan(basePackages = {
    "com.helios.etl.model",
    "com.helios.etl.progress.entities"
})
@EnableTransactionManagement
public class EtlCommandLineRunner implements CommandLineRunner {

    public static final Logger log = LoggerFactory.getLogger(EtlCommandLineRunner.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private Tranformer transformer;

    @Autowired
    private CacheMemory cacheMemory;

    @Autowired
    private HeliosFilesService heliosFilesService;

    @Autowired
    private com.helios.etl.progress.services.ProgressTrackingService progressTrackingService;

    public static void main(String[] args)
    {
        log.info("Starting ETL CLI...");
        SpringApplication.run(EtlCommandLineRunner.class, args);
        log.info("ETL CLI completed");
    }

    @Override
    public void run(String... args) {
        // If no args are provided, do nothing (app can run normally)
//        if (args == null || args.length == 0) {
//            return;
//        }
        // Simple CLI: --run starts the ETL, --help shows usage
        boolean run = true;
        boolean force = true;
        for (String arg : args) {
            if ("--run".equalsIgnoreCase(arg) || "run".equalsIgnoreCase(arg)) {
                run = true;
            }
            if ("--force".equalsIgnoreCase(arg) || "-f".equalsIgnoreCase(arg) || "force".equalsIgnoreCase(arg)) {
                force = true;
            }
            if ("--help".equalsIgnoreCase(arg) || "-h".equalsIgnoreCase(arg) || "help".equalsIgnoreCase(arg)) {
                printHelp();
                exit(0);
                return;
            }
        }

        if (!run) {
            printHelp();
            exit(1);
            return;
        }

        try {
            if (force) {
                int cleared = progressTrackingService.failAllRunningRuns("Force start requested by CLI");
                if (cleared > 0) {
                    log.warn("Cleared {} stale ETL run(s) before starting a new run due to --force.", cleared);
                }
            }
            int total = cacheMemory.getTickets() != null ? cacheMemory.getTickets().size() : 0;
            log.info("Starting ETL transformation for {} tickets...", total);
            ProgressBar bar = new ProgressBar(total);

            boolean ok = transformer.Run(new ProgressListener() {
                @Override
                public void onProgress(int t, int current, String note) {
                    bar.stepTo(current, note);
                }
            }, "run-etl");

            // Ensure progress bar finishes
            bar.finish();

            if (!ok) {
                log.error("ETL run failed");
                exit(2);
            } else {
                log.info("ETL run completed successfully");
                exit(0);
            }

            cacheMemory.setEtlRunEnd(false, "");
        } catch (Exception e) {
            log.error("ETL execution error", e);
            System.err.println("Error: " + e.getMessage());
            cacheMemory.setEtlRunEnd(true, e.getMessage());
            exit(2);
        }
    }

    private void printHelp() {
        System.out.println("ETL CLI");
        System.out.println("Usage: java -jar etl.jar --run [--force]");
        System.out.println("Options: --help | -h | --force | -f");
        System.out.println("--force: Clear any stale running ETL locks and start a new run");
    }

    private void exit(int code) {
        SpringApplication.exit(applicationContext, () -> code);
        System.exit(code);
    }

    // Simple console progress bar
    static class ProgressBar {
        private final int total;
        private int lastPrinted = -1;

        ProgressBar(int total) {
            this.total = Math.max(total, 0);
            render(0, "");
        }

        void stepTo(int current, String note) {
            render(current, note);
        }

        void finish() {
            render(total, "done");
            System.out.println();
        }

        private void render(int current, String note) {
            if (total <= 0) {
                System.out.print("\r[==========] 100% " + (note == null ? "" : note));
                return;
            }
            current = Math.min(current, total);
            int percent = (int) Math.round(100.0 * current / total);
            // avoid excessive console updates
            if (percent == lastPrinted) return;
            lastPrinted = percent;

            int barLen = 30;
            int filled = (int) Math.round(barLen * (percent / 100.0));
            String bar = "=".repeat(filled) + " ".repeat(barLen - filled);
            System.out.print("\r[" + bar + "] " + String.format("%3d", percent) + "% (" + current + "/" + total + ") " + (note == null ? "" : note));
            System.out.flush();
        }
    }
}
