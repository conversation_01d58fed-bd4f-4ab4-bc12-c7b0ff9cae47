package com.helios.etl.cli;

import com.helios.etl.services.CacheMemory;
import com.helios.etl.services.ProgressListener;
import com.helios.etl.services.Tranformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Processes ETL CLI commands with progress tracking support
 */
@Component
public class EtlCliCommandProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(EtlCliCommandProcessor.class);
    
    @Autowired
    private Tranformer transformer;
    
    @Autowired
    private CacheMemory cacheMemory;
    
    @Autowired
    private ProgressCommandHandler progressCommandHandler;
    
    /**
     * Process CLI command and return exit code
     */
    public int processCommand(String[] args) {
        if (args.length == 0 || containsHelp(args)) {
            printHelp();
            return 0;
        }
        
        String command = extractCommand(args);
        
        try {
            switch (command.toLowerCase()) {
                case "run-etl":
                case "full-etl":
                    return runFullEtl();
                    
                case "status":
                    progressCommandHandler.showStatus();
                    return 0;
                    
                case "progress":
                    progressCommandHandler.showDetailedProgress(null);
                    return 0;
                    
                case "resume-info":
                    progressCommandHandler.showResumeInfo();
                    return 0;
                    
                case "resume-etl":
                    return resumeEtl();
                    
                case "list-runs":
                    progressCommandHandler.listRecentRuns(10);
                    return 0;
                    
                default:
                    System.err.println("❌ Unknown command: " + command);
                    printHelp();
                    return 1;
            }
        } catch (Exception e) {
            log.error("Command execution failed", e);
            System.err.println("❌ Command failed: " + e.getMessage());
            return 2;
        }
    }
    
    private int runFullEtl() {
        log.info("🚀 Starting full ETL process...");
        
        int total = cacheMemory.getTickets() != null ? cacheMemory.getTickets().size() : 0;
        log.info("Starting ETL transformation for {} tickets...", total);
        
        ProgressBar bar = new ProgressBar(total);
        
        boolean ok = transformer.Run(new ProgressListener() {
            @Override
            public void onProgress(int t, int current, String note) {
                bar.stepTo(current, note);
            }
        }, "run-etl");
        
        bar.finish();
        
        if (!ok) {
            log.error("❌ ETL run failed");
            return 2;
        } else {
            log.info("✅ ETL run completed successfully");
            return 0;
        }
    }
    
    private int resumeEtl() {
        log.info("🔄 Attempting to resume ETL process...");
        System.out.println("🔄 Resume ETL functionality");
        System.out.println("═══════════════════════════");
        System.out.println("⚠️  Resume functionality is not yet fully implemented.");
        System.out.println("💡 This feature will allow resuming from the last successful ticket.");
        System.out.println("📋 Use 'status' command to see current progress state.");
        return 0;
    }
    
    private boolean containsHelp(String[] args) {
        for (String arg : args) {
            if ("--help".equals(arg) || "-h".equals(arg)) {
                return true;
            }
        }
        return false;
    }
    
    private String extractCommand(String[] args) {
        for (int i = 0; i < args.length - 1; i++) {
            if ("--command".equals(args[i]) || "-c".equals(args[i])) {
                return args[i + 1];
            }
        }
        // Default command if none specified
        return "run-etl";
    }
    
    private void printHelp() {
        System.out.println("🔧 ETL CLI - Helios Data Migration Tool");
        System.out.println("═══════════════════════════════════════");
        System.out.println();
        System.out.println("Usage: java -jar etl.jar [options]");
        System.out.println();
        System.out.println("Commands:");
        System.out.println("  --command run-etl      Run full ETL process (default)");
        System.out.println("  --command full-etl     Alias for run-etl");
        System.out.println("  --command status       Show current ETL status");
        System.out.println("  --command progress     Show detailed progress information");
        System.out.println("  --command resume-info  Show information about resumable runs");
        System.out.println("  --command resume-etl   Resume ETL from last successful point");
        System.out.println("  --command list-runs    List recent ETL runs");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  --help, -h            Show this help message");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar etl.jar --command run-etl");
        System.out.println("  java -jar etl.jar --command status");
        System.out.println("  java -jar etl.jar --command resume-etl");
        System.out.println();
        System.out.println("Features:");
        System.out.println("  ✅ Real-time progress tracking");
        System.out.println("  ✅ SQLite-based progress persistence");
        System.out.println("  ✅ Error tracking and reporting");
        System.out.println("  ✅ Resume from last successful point");
        System.out.println("  ✅ Detailed status and progress information");
    }
    
    // Simple console progress bar (moved from EtlCommandLineRunner)
    static class ProgressBar {
        private final int total;
        private int lastPrinted = -1;

        ProgressBar(int total) {
            this.total = Math.max(total, 0);
            render(0, "");
        }

        void stepTo(int current, String note) {
            render(current, note);
        }

        void finish() {
            render(total, "done");
            System.out.println();
        }

        private void render(int current, String note) {
            if (total <= 0) {
                System.out.print("\r[==========] 100% " + (note == null ? "" : note));
                return;
            }
            current = Math.min(current, total);
            int percent = (int) Math.round(100.0 * current / total);
            // avoid excessive console updates
            if (percent == lastPrinted) return;
            lastPrinted = percent;

            int barLen = 30;
            int filled = (int) Math.round(barLen * (percent / 100.0));
            String bar = "=".repeat(filled) + " ".repeat(barLen - filled);
            System.out.print("\r[" + bar + "] " + String.format("%3d", percent) + "% (" + current + "/" + total + ") " + (note == null ? "" : note));
            System.out.flush();
        }
    }
}
