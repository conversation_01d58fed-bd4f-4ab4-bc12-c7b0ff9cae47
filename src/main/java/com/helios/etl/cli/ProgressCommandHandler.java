package com.helios.etl.cli;

import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.services.EtlResumeService;
import com.helios.etl.progress.services.ProgressTrackingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Handles CLI commands related to ETL progress tracking
 */
@Component
public class ProgressCommandHandler {
    
    private static final Logger log = LoggerFactory.getLogger(ProgressCommandHandler.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private ProgressTrackingService progressTrackingService;
    
    @Autowired
    private EtlResumeService resumeService;
    
    /**
     * Show current ETL status
     */
    public void showStatus() {
        System.out.println("🔍 ETL Progress Status");
        System.out.println("═══════════════════════");
        
        // Check for running processes
        if (progressTrackingService.isEtlRunning()) {
            System.out.println("⚠️  ETL process is currently RUNNING");
            
            Optional<EtlRun> lastRun = progressTrackingService.getLastEtlRun();
            if (lastRun.isPresent()) {
                displayEtlRunInfo(lastRun.get());
            }
        } else {
            System.out.println("✅ No ETL process currently running");
            
            // Show last completed run
            Optional<EtlRun> lastRun = progressTrackingService.getLastEtlRun();
            if (lastRun.isPresent()) {
                System.out.println("\n📋 Last ETL Run:");
                displayEtlRunInfo(lastRun.get());
            } else {
                System.out.println("📋 No previous ETL runs found");
            }
        }
        
        // Check for incomplete runs
        if (resumeService.hasIncompleteRuns()) {
            System.out.println("\n⚠️  Incomplete ETL runs detected!");
            System.out.println("   Use --command resume-etl to continue from last successful point");
            
            Optional<EtlRun> incompleteRun = resumeService.getLastIncompleteRun();
            if (incompleteRun.isPresent()) {
                EtlResumeService.ResumeInfo resumeInfo = resumeService.getResumeInfo(incompleteRun.get().getId());
                if (resumeInfo != null) {
                    displayResumeInfo(resumeInfo);
                }
            }
        }
    }
    
    /**
     * Show detailed progress information
     */
    public void showDetailedProgress(Long etlRunId) {
        if (etlRunId == null) {
            Optional<EtlRun> lastRun = progressTrackingService.getLastEtlRun();
            if (lastRun.isEmpty()) {
                System.out.println("❌ No ETL runs found");
                return;
            }
            etlRunId = lastRun.get().getId();
        }
        
        ProgressTrackingService.EtlRunProgress progress = progressTrackingService.getEtlRunProgress(etlRunId);
        if (progress == null) {
            System.out.println("❌ ETL run not found: " + etlRunId);
            return;
        }
        
        System.out.println("📊 Detailed ETL Progress");
        System.out.println("═══════════════════════");
        System.out.println("Run ID: " + progress.runId);
        System.out.println("Status: " + progress.status);
        System.out.println("Started: " + progress.startTime.format(FORMATTER));
        if (progress.endTime != null) {
            System.out.println("Ended: " + progress.endTime.format(FORMATTER));
        }
        System.out.println();
        
        System.out.printf("Progress: %.1f%% (%d/%d tickets)%n", 
            progress.progressPercentage, progress.processedTickets, progress.totalTickets);
        System.out.printf("✅ Successful: %d%n", progress.successfulTickets);
        System.out.printf("❌ Failed: %d%n", progress.failedTickets);
        System.out.printf("🔍 Total Errors: %d%n", progress.totalErrors);
        
        // Show progress bar
        displayProgressBar(progress.progressPercentage);
    }
    
    /**
     * Show resume information
     */
    public void showResumeInfo() {
        if (!resumeService.hasIncompleteRuns()) {
            System.out.println("✅ No incomplete ETL runs found. Nothing to resume.");
            return;
        }
        
        Optional<EtlRun> incompleteRun = resumeService.getLastIncompleteRun();
        if (incompleteRun.isEmpty()) {
            System.out.println("❌ Could not find incomplete run details");
            return;
        }
        
        EtlResumeService.ResumeInfo resumeInfo = resumeService.getResumeInfo(incompleteRun.get().getId());
        if (resumeInfo == null) {
            System.out.println("❌ Could not retrieve resume information");
            return;
        }
        
        System.out.println("🔄 ETL Resume Information");
        System.out.println("═══════════════════════");
        displayResumeInfo(resumeInfo);
        
        System.out.println("\n💡 To resume the ETL process, run:");
        System.out.println("   etl-cli.bat --command resume-etl");
    }
    
    private void displayEtlRunInfo(EtlRun etlRun) {
        System.out.println("   Run ID: " + etlRun.getRunId());
        System.out.println("   Status: " + etlRun.getStatus());
        System.out.println("   Started: " + etlRun.getStartTime().format(FORMATTER));
        if (etlRun.getEndTime() != null) {
            System.out.println("   Ended: " + etlRun.getEndTime().format(FORMATTER));
        }
        if (etlRun.getTotalTickets() != null) {
            System.out.printf("   Progress: %.1f%% (%d/%d tickets)%n", 
                etlRun.getProgressPercentage(), 
                etlRun.getProcessedTickets() != null ? etlRun.getProcessedTickets() : 0, 
                etlRun.getTotalTickets());
        }
    }
    
    private void displayResumeInfo(EtlResumeService.ResumeInfo resumeInfo) {
        System.out.println("   Run ID: " + resumeInfo.runId);
        System.out.println("   Started: " + resumeInfo.startTime.format(FORMATTER));
        System.out.printf("   Progress: %.1f%% (%d/%d tickets)%n", 
            resumeInfo.getProgressPercentage(), resumeInfo.processedTickets, resumeInfo.totalTickets);
        System.out.printf("   ✅ Successful: %d%n", resumeInfo.successfulTickets);
        System.out.printf("   ❌ Failed: %d%n", resumeInfo.failedTickets);
        System.out.printf("   ⏳ Remaining: %d tickets%n", resumeInfo.remainingTickets);
        if (resumeInfo.lastProcessedTicketId != null) {
            System.out.printf("   📍 Last processed ticket: %d%n", resumeInfo.lastProcessedTicketId);
        }
    }
    
    private void displayProgressBar(double percentage) {
        int barLength = 40;
        int filled = (int) (barLength * percentage / 100.0);
        String bar = "█".repeat(filled) + "░".repeat(barLength - filled);
        System.out.printf("[%s] %.1f%%%n", bar, percentage);
    }
    
    /**
     * List recent ETL runs
     */
    public void listRecentRuns(int limit) {
        System.out.println("📋 Recent ETL Runs");
        System.out.println("═══════════════════");
        
        // This would need to be implemented in ProgressTrackingService
        System.out.println("Feature coming soon: List of recent ETL runs");
    }
}
