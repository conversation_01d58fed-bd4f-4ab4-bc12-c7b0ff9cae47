package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.Tickets;
import com.helios.etl.source.helper.DefaultHelper;
import com.helios.etl.source.helper.IdToStringMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public class TicketsRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public TicketsRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<Tickets> rowMapper = new RowMapper<Tickets>() {
        @Override
        public Tickets mapRow(ResultSet rs, int rowNum) throws SQLException {
            Tickets t = new Tickets();
            t.setIdTickets(rs.getInt("idTickets"));
            t.setCtNum(rs.getString("ct_num"));
            t.setClient(rs.getString("client"));
            t.setDateCreation(getSafeDateTime(rs, "dateCreation"));
            t.setDemandeur(rs.getString("demandeur"));
            t.setAssigne(rs.getString("assigne"));
            t.setPole(rs.getString("pole"));
            t.setType(rs.getString("type"));
            t.setStatus(rs.getString("status"));
            t.setPriorite(rs.getString("priorite"));
            t.setNiveau(rs.getInt("niveau"));
            t.setCategorie(rs.getString("categorie"));
            t.setCategorie2(rs.getString("categorie2"));
            t.setCategorie3(rs.getString("categorie3"));
            t.setTempsTotal(rs.getInt("tempsTotal"));
            t.setTitre(rs.getString("titre"));
            t.setDescription(rs.getString("description"));
            t.setDateRappel(getSafeDateTime(rs, "dateRappel"));
            t.setDateResolution(getSafeDateTime(rs, "dateResolution"));
            t.setStatusTemps(rs.getInt("statusTemps"));
            t.setAvertissement(rs.getInt("avertissement"));
            t.setDernierCorrespondant(rs.getString("dernierCorrespondant"));
            t.setDatePremiereReponse(getSafeDateTime(rs, "datePremiereReponse"));
            t.setDateDerniereReponse(getSafeDateTime(rs, "dateDerniereReponse"));
            t.setNotificationEnabled(rs.getBoolean("notificationEnabled"));
            return t;
        }

        private LocalDateTime getSafeDateTime(ResultSet rs, String column) throws SQLException {
            Timestamp ts = rs.getTimestamp(column);
            return ts != null ? ts.toLocalDateTime() : DefaultHelper.DEFAULT_MIN_DATE;
        }
    };

    /**
     * Retrieves all tickets that are active and have been created more than 2 years ago.
     * The results are ordered by idTickets in descending order.
     *
     * @return List of active Tickets
     */
    public List<Tickets> getAll(int pole) {
        String sql = "SELECT * FROM Tickets WHERE dateCreation > DATEADD(year, -1, GETDATE()) " +
                "AND status <> 'Inactif' " +
                "AND status = N'Résolu' " +
                (pole > 0 ? "AND pole = ? " : "") +
                "ORDER BY idTickets DESC";

        if (pole > 0) {
            return jdbcTemplate.query(sql, rowMapper, IdToStringMapper.idPoleToString(pole));
        } else {
            return jdbcTemplate.query(sql, rowMapper);
        }
    }

    public Tickets getById(int id) {
        String sql = "SELECT * FROM Tickets WHERE idTickets = ?";
        List<Tickets> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(Tickets entity) {
        String sql = "INSERT INTO Tickets (ct_num, client, dateCreation, demandeur, assigne, pole, type, status, priorite, niveau, categorie, categorie2, categorie3, tempsTotal, titre, description, dateRappel, dateResolution, statusTemps, avertissement, dernierCorrespondant, datePremiereReponse, dateDerniereReponse, notificationEnabled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        jdbcTemplate.update(sql,
                entity.getCtNum(),
                entity.getClient(),
                entity.getDateCreation(),
                entity.getDemandeur(),
                entity.getAssigne(),
                entity.getPole(),
                entity.getType(),
                entity.getStatus(),
                entity.getPriorite(),
                entity.getNiveau(),
                entity.getCategorie(),
                entity.getCategorie2(),
                entity.getCategorie3(),
                entity.getTempsTotal(),
                entity.getTitre(),
                entity.getDescription(),
                entity.getDateRappel(),
                entity.getDateResolution(),
                entity.getStatusTemps(),
                entity.getAvertissement(),
                entity.getDernierCorrespondant(),
                entity.getDatePremiereReponse(),
                entity.getDateDerniereReponse(),
                entity.isNotificationEnabled()
        );
    }

    public void update(Tickets entity) {
        String sql = "UPDATE Tickets SET ct_num = ?, client = ?, dateCreation = ?, demandeur = ?, assigne = ?, pole = ?, type = ?, status = ?, priorite = ?, niveau = ?, categorie = ?, categorie2 = ?, categorie3 = ?, tempsTotal = ?, titre = ?, description = ?, dateRappel = ?, dateResolution = ?, statusTemps = ?, avertissement = ?, dernierCorrespondant = ?, datePremiereReponse = ?, dateDerniereReponse = ?, notificationEnabled = ? WHERE idTickets = ?";
        jdbcTemplate.update(sql,
                entity.getCtNum(),
                entity.getClient(),
                entity.getDateCreation(),
                entity.getDemandeur(),
                entity.getAssigne(),
                entity.getPole(),
                entity.getType(),
                entity.getStatus(),
                entity.getPriorite(),
                entity.getNiveau(),
                entity.getCategorie(),
                entity.getCategorie2(),
                entity.getCategorie3(),
                entity.getTempsTotal(),
                entity.getTitre(),
                entity.getDescription(),
                entity.getDateRappel(),
                entity.getDateResolution(),
                entity.getStatusTemps(),
                entity.getAvertissement(),
                entity.getDernierCorrespondant(),
                entity.getDatePremiereReponse(),
                entity.getDateDerniereReponse(),
                entity.isNotificationEnabled(),
                entity.getIdTickets()
        );
    }

    public void delete(Tickets entity) {
        String sql = "DELETE FROM Tickets WHERE idTickets = ?";
        jdbcTemplate.update(sql, entity.getIdTickets());
    }
}