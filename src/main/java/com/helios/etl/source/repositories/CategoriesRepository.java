package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.Categorie;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@Repository
public class CategoriesRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public CategoriesRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<Categorie> rowMapper = new RowMapper<Categorie>() {
        @Override
        public Categorie mapRow(ResultSet rs, int rowNum) throws SQLException {
            Categorie categorie = new Categorie();
            categorie.setLibelle(rs.getString("libelle"));
            categorie.setIdPole(rs.getInt("idPole"));
            return categorie;
        }
    };

    public List<Categorie> getAll() {
        String sql = "SELECT libelle, idPole FROM Categorie";
        return jdbcTemplate.query(sql, rowMapper);
    }

    public Categorie getById(int id) {
        String sql = "SELECT libelle, idPole FROM Categorie WHERE idPole = ?";
        List<Categorie> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(Categorie entity) {
        String sql = "INSERT INTO Categorie (libelle, idPole) VALUES (?, ?)";
        jdbcTemplate.update(sql, entity.getLibelle(), entity.getIdPole());
    }

    public void update(Categorie entity) {
        String sql = "UPDATE Categorie SET libelle = ? WHERE idPole = ?";
        jdbcTemplate.update(sql, entity.getLibelle(), entity.getIdPole());
    }

    public void delete(Categorie entity) {
        String sql = "DELETE FROM Categorie WHERE idPole = ?";
        jdbcTemplate.update(sql, entity.getIdPole());
    }
}