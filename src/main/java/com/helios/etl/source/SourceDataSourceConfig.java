package com.helios.etl.source;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;

@Configuration
public class SourceDataSourceConfig {
    @Value("${sqlserver.url}")
    private String url;

    @Value("${sqlserver.username}")
    private String username;

    @Value("${sqlserver.password}")
    private String password;

    @Value("${sqlserver.driver-class-name}")
    private String driverClassName;

    @Bean(name = "sourceSqlServerDataSource")
    public DataSource sourceSqlServerDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(driverClassName);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        return dataSource;
    }

    @Bean(name = "sourceSqlServerJdbcTemplate")
    public JdbcTemplate sourceSqlServerJdbcTemplate(@Qualifier("sourceSqlServerDataSource") DataSource sourceSqlServerDataSource) {
        return new JdbcTemplate(sourceSqlServerDataSource);
    }
}
