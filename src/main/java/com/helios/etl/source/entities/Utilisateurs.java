package com.helios.etl.source.entities;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Utilisateurs {
    private Integer idUtilisateur;
    private String utilisateur;
    private String societe;
    private String nom;
    private String prenom;
    private String email;
    private String telephone;
    private String mdp;
    private Integer idPole;
    private String multiPole;
    private Integer groupe;
    private String service;
    private Boolean terrain;
    private Boolean admin;
    private LocalDateTime derniereConnexion;
    private Boolean actif;
    private Boolean droitContact;
    private Boolean droitPret;
    private Boolean droitContrat;
    private Boolean droitTemps;
    private Boolean droitModele;
    private Boolean droitUtilisateur;
    private Boolean droitReponse;
    private Boolean droitWildix;
    private Boolean droitCommande;
    private Boolean droitObjectif;
    private Boolean droitPole;
    private Boolean droitQualiopi;
    private Boolean droitQualiteSaisie;
    private Boolean droitPlanification;
    private Boolean droitPieces;
    private Boolean suiviMail;
    private String cheminCommandePJ;
    private Integer droitInfogerances;
    private Boolean droitADV;
    private Boolean droitAbbot;
    private Boolean droitLicenceLogiciel;
    private Boolean droitDemandeBureautique;

    /**
     * Converts this Utilisateur entity to a Map<String, String>
     * @return Map containing all user fields as string key-value pairs
     */
    public Map<String, String> toMap() {
        Map<String, String> userMap = new HashMap<>();

        userMap.put("idUtilisateur", String.valueOf(idUtilisateur));
        userMap.put("utilisateur", utilisateur != null ? utilisateur : "");
        userMap.put("societe", societe != null ? societe : "");
        userMap.put("nom", nom != null ? nom : "");
        userMap.put("prenom", prenom != null ? prenom : "");
        userMap.put("email", email != null ? email : "");
        userMap.put("telephone", telephone != null ? telephone : "");
        userMap.put("mdp", mdp != null ? mdp : "");
        userMap.put("idPole", String.valueOf(idPole));
        userMap.put("multiPole", String.valueOf(multiPole));
        userMap.put("groupe", String.valueOf(groupe));
        userMap.put("service", service != null ? service : "");
        userMap.put("terrain", String.valueOf(terrain));
        userMap.put("admin", String.valueOf(admin));
        userMap.put("derniereConnexion", derniereConnexion != null ? derniereConnexion.toString() : "");
        userMap.put("actif", String.valueOf(actif));
        userMap.put("droitContact", String.valueOf(droitContact));
        userMap.put("droitPret", String.valueOf(droitPret));
        userMap.put("droitContrat", String.valueOf(droitContrat));
        userMap.put("droitTemps", String.valueOf(droitTemps));
        userMap.put("droitModele", String.valueOf(droitModele));
        userMap.put("droitUtilisateur", String.valueOf(droitUtilisateur));
        userMap.put("droitReponse", String.valueOf(droitReponse));
        userMap.put("droitWildix", String.valueOf(droitWildix));
        userMap.put("droitCommande", String.valueOf(droitCommande));
        userMap.put("droitObjectif", String.valueOf(droitObjectif));
        userMap.put("droitPole", String.valueOf(droitPole));
        userMap.put("droitQualiopi", String.valueOf(droitQualiopi));
        userMap.put("droitQualiteSaisie", String.valueOf(droitQualiteSaisie));
        userMap.put("droitPlanification", String.valueOf(droitPlanification));
        userMap.put("droitPieces", String.valueOf(droitPieces));
        userMap.put("suiviMail", String.valueOf(suiviMail));
        userMap.put("cheminCommandePJ", cheminCommandePJ != null ? cheminCommandePJ : "");
        userMap.put("droitInfogerances", String.valueOf(droitInfogerances));
        userMap.put("droitADV", String.valueOf(droitADV));
        userMap.put("droitAbbot", String.valueOf(droitAbbot));
        userMap.put("droitLicenceLogiciel", String.valueOf(droitLicenceLogiciel));
        userMap.put("droitDemandeBureautique", String.valueOf(droitDemandeBureautique));

        return userMap;
    }
}