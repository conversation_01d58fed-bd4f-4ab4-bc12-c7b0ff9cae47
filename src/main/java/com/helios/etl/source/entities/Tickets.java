package com.helios.etl.source.entities;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
public class Tickets {
    private int idTickets;
    private String ctNum = "";
    private String client = "";
    private LocalDateTime dateCreation = LocalDateTime.now();
    private String demandeur = "";
    private String assigne = "";
    private String pole = "";
    private String type = "";
    private String status = "";
    private String priorite = "";
    private int niveau;
    private String categorie = "";
    private String categorie2 = "";
    private String categorie3 = "";
    private int tempsTotal;
    private String titre = "";
    private String description = "";
    private LocalDateTime dateRappel = LocalDateTime.now();
    private LocalDateTime dateResolution = LocalDateTime.now();
    private int statusTemps;
    private int avertissement;
    private String dernierCorrespondant = "";
    private LocalDateTime datePremiereReponse = LocalDateTime.now();
    private LocalDateTime dateDerniereReponse = LocalDateTime.now();
    private boolean notificationEnabled = false;

    /**
     * Converts this Ticket entity to a Map<String, String>
     * @return Map containing all ticket fields as string key-value pairs
     */
    public Map<String, String> toMap() {
        Map<String, String> ticketMap = new HashMap<>();

        ticketMap.put("heliosv1.idTickets", String.valueOf(idTickets));
        ticketMap.put("heliosv1.ctNum", ctNum != null ? ctNum : "");
        ticketMap.put("heliosv1.client", client != null ? client : "");
        ticketMap.put("heliosv1.dateCreation", dateCreation != null ? dateCreation.toString() : "");
        ticketMap.put("heliosv1.demandeur", demandeur != null ? demandeur : "");
        ticketMap.put("heliosv1.assigne", assigne != null ? assigne : "");
        ticketMap.put("heliosv1.pole", pole != null ? pole : "");
        ticketMap.put("heliosv1.type", type != null ? type : "");
        ticketMap.put("heliosv1.status", status != null ? status : "");
        ticketMap.put("heliosv1.priorite", priorite != null ? priorite : "");
        ticketMap.put("heliosv1.niveau", String.valueOf(niveau));
        ticketMap.put("heliosv1.categorie", categorie != null ? categorie : "");
        ticketMap.put("heliosv1.categorie2", categorie2 != null ? categorie2 : "");
        ticketMap.put("heliosv1.categorie3", categorie3 != null ? categorie3 : "");
        ticketMap.put("heliosv1.tempsTotal", String.valueOf(tempsTotal));
        ticketMap.put("heliosv1.titre", titre != null ? titre : "");
        ticketMap.put("heliosv1.description", description != null ? description : "");
        ticketMap.put("heliosv1.dateRappel", dateRappel != null ? dateRappel.toString() : "");
        ticketMap.put("heliosv1.dateResolution", dateResolution != null ? dateResolution.toString() : "");
        ticketMap.put("heliosv1.statusTemps", String.valueOf(statusTemps));
        ticketMap.put("heliosv1.avertissement", String.valueOf(avertissement));
        ticketMap.put("heliosv1.dernierCorrespondant", dernierCorrespondant != null ? dernierCorrespondant : "");
        ticketMap.put("heliosv1.datePremiereReponse", datePremiereReponse != null ? datePremiereReponse.toString() : "");
        ticketMap.put("heliosv1.dateDerniereReponse", dateDerniereReponse != null ? dateDerniereReponse.toString() : "");
        ticketMap.put("heliosv1.notificationEnabled", String.valueOf(notificationEnabled));

        return ticketMap;
    }
}