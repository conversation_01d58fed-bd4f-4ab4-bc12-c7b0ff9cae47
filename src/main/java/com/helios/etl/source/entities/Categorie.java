package com.helios.etl.source.entities;

public class Categorie {
    private String Libelle;
    private int IdPole;

    public String getLibelle() { return Libelle; }
    public void setLibelle(String libelle) { Libelle = libelle; }

    public int getIdPole() { return IdPole; }
    public void setIdPole(int idPole) { IdPole = idPole; }

    public void setIdPole(String poleName) {
        poleName = poleName.toLowerCase();
        switch (poleName)
        {
            case "informatique":
                IdPole = 1;
                break;
            case "logiciel":
                IdPole = 2;
                break;
            case "bureautique":
                IdPole = 3;
                break;
            default:
                IdPole = 0;
                break;
        }
    }
}
