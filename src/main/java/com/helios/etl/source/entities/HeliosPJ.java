package com.helios.etl.source.entities;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Représente une piece jointe dans le système Helios (Que cela soit PJ d'une commande ou d'un ticket)
 * Ne représente pas une table de la base de données, il s'agit d'une structure de données intermédiaire
 */
@Data
@NoArgsConstructor
public class HeliosPJ {
    public int numero;
    public String nom;
    public String path;
    public Tickets tickets;
    public Commandes commandes;
    public Byte[] contenu;
    public String typeMime;
    public long taille;

    @Override
    public String toString() {
        return "HeliosPJ{" +
                "numero=" + numero +
                ", nom='" + nom + '\'' +
                ", path='" + path + '\'' +
                ", tickets=" + tickets +
                ", commandes=" + commandes +
                '}';
    }
}
