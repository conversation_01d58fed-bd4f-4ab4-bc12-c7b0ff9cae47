/**
 *
 */
package com.helios.etl.model;

import com.helios.etl.outer.spring.jpa.TypeRelationIssueUserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_issue_relation")
public class IssueRelation {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_oid", nullable = false)
    @Getter
    @Setter
    protected AbstractIssue source;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cible_oid", nullable = false)
    @Getter
    @Setter
    protected AbstractIssue cible;

    /**
     *
     */
    @Enumerated(EnumType.STRING)
    @Type(value = TypeRelationIssueUserType.class)
    @Column(nullable = false, length = 25)
    @Getter
    @Setter
    protected TypeRelationIssue type = TypeRelationIssue.RELIE_A;
}
