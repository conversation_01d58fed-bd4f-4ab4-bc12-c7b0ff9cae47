/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Embeddable
@Access(AccessType.FIELD)
public class AdresseLivraison implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -5328396076009321291L;

    /**
     * Complément d’identification du destinataire ou point de remise à l’intérieur du bâtiment :
     * N° appartement, boite aux lettres, étage, couloir
     */
    @Column(name = "liv_complementDestinataire", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String complementDestinataire = "";

    /**
     * Complément d’identification du point géographique – extérieur du bâtiment :
     * entrée, tour, bâtiment, immeuble, résidence
     */
    @Column(name = "liv_complementGeographique", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String complementGeographique = "";

    /**
     * Numéro et libellé de la voie
     */
    @Column(name = "liv_libelle", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String libelle = "";

    /**
     *
     */
    @Column(name = "liv_complementLibelle", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String complementLibelle = "";

    /**
     *
     */
    @Column(name = "liv_codePostal", length = 10)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String codePostal = "";

    /**
     *
     */
    @Column(name = "liv_communeLibelle", length = 150)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String communeLibelle = "";

    /**
     *
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "liv_commune_oid")
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected Commune commune;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "liv_pays_oid")
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected Pays pays;

    /**
     *
     */
    public AdresseLivraison() {}
}
