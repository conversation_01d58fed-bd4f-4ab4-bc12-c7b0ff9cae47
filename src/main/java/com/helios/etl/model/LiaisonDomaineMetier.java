/**
 *
 */
package com.helios.etl.model;

import java.util.Collection;
import java.util.Iterator;

/**
 * <AUTHOR>
 *
 */
public interface LiaisonDomaineMetier {
    /**
     * @return
     */
    public Collection<DomaineMetier> getDomainesMetier();

    /**
     * @param domaines
     */
    public void setDomainesMetier(Collection<DomaineMetier> domaines);

    /**
     * Renvoi un iterateur sur la collection de domaines
     * @return
     */
    public default Iterator<DomaineMetier> domainesMetierIterator() {
        return getDomainesMetier().iterator();
    }

    /**
     * Un iterateur sur la collection de domaines va supprimer l'élément avec la métode remove
     * @return
     */
    public default void iteratorWillRemoveLiaisonDomaineMetier(
        DomaineMetier domaine
    ) {}

    /**
     * Un iterateur sur la collection de domaines a supprimé l'élément avec la métode remove
     * @return
     */
    public default void iteratorHasRemovedLiaisonDomaineMetier(
        DomaineMetier domaine
    ) {}

    /**
     * Ajout un domaine à la collection
     * @return
     */
    public default boolean addLiaisonDomaineMetier(DomaineMetier domaine) {
        return getDomainesMetier().add(domaine);
    }

    /**
     * Supprimer un domaine de la collection
     * @return
     */
    public default boolean removeLiaisonDomaineMetier(DomaineMetier domaine) {
        return getDomainesMetier().remove(domaine);
    }

    /**
     * Renvoie vrai si une liaison avec ce domaine existe
     * @return
     */
    public default boolean hasLiaisonDomaineMetier(DomaineMetier domaine) {
        if (domaine == null || getDomainesMetier() == null) {
            return false;
        }

        for (DomaineMetier e : getDomainesMetier()) {
            if (e.getOid() == domaine.getOid()) {
                return true;
            }
        }

        return false;
    }
}
