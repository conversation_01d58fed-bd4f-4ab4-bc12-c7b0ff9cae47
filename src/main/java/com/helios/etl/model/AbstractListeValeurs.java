/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import java.io.Serializable;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@MappedSuperclass
public abstract class AbstractListeValeurs
    implements LiaisonDomaineMetier, Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -3823340080248731244L;

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * Code fonctionnel court et unique, utilisé pour les intégrations, exports ou l’identification rapide.
     */
    @Column(length = 50, unique = true, nullable = false)
    @Getter
    @Setter
    protected String code = "";

    /**
     * Nom lisible affiché dans l’interface utilisateur.
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    protected String libelle = "";

    /**
     * Texte explicatif plus détaillé, destiné à documenter les finalités, responsabilités et périmètre de ce niveau
     */
    @Column(nullable = false, columnDefinition = "text")
    @Getter
    @Setter
    protected String description = "";

    /**
     *
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean obsolete = false;

    /**
     * Options attachées à la valeur
     */
    @Type(JsonStringType.class)
    @Column(columnDefinition = "json", nullable = true)
    @Getter
    @Setter
    protected Map<String, String> options = null;
}
