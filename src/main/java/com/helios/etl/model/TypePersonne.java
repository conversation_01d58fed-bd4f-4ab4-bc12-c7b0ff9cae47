/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_personne_type")
public class TypePersonne extends AbstractListeValeurs {

    /**
     *
     */
    private static final long serialVersionUID = 6669593652655823490L;

    /**
     * Ce type est interne à l'organisation
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean interne = false;

    /**
     * Lien avec le domaine
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_personne_type_has_domaine_metier",
        joinColumns = @JoinColumn(
            name = "personne_type_oid",
            referencedColumnName = "oid"
        ),
        inverseJoinColumns = @JoinColumn(
            name = "domaine_metier_oid",
            referencedColumnName = "oid"
        )
    )
    @Getter
    @Setter
    @JsonIgnore
    protected Collection<DomaineMetier> domainesMetier;
}
