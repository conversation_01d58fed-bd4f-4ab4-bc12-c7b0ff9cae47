/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_issue_statut")
public class IssueStatut extends AbstractListeValeurs {

    /**
     *
     */
    private static final long serialVersionUID = 7084317440652886418L;

    /**
     * indique si le statut est considéré comme nouveau.
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean nouveau = false;

    /**
     * indique si le statut est considéré comme fermé.
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean ferme = false;

    /**
     * Lien avec le domaine
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_issue_statut_has_domaine_metier",
        joinColumns = @JoinColumn(
            name = "issue_statut_oid",
            referencedColumnName = "oid"
        ),
        inverseJoinColumns = @JoinColumn(
            name = "domaine_metier_oid",
            referencedColumnName = "oid"
        )
    )
    @Getter
    @Setter
    @JsonIgnore
    protected Collection<DomaineMetier> domainesMetier;
}
