/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.io.Serializable;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "cog_pays")
public class Pays implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 7852983944276266302L;

    /**
     *
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected long oid;

    /**
     *
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * Libellé utilisé dans le COG
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String libelle = "";

    /**
     * Libellé du nom entier développé et enrichi paru au J.O. du 25 janvier 1994
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String libelleEnrichi = "";

    /**
     * Ancien nom du pays
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String ancienNom = "";

    /**
     * Code du pays sur 2 caractères conforme à la norme internationale ISO 3166 de représentation des noms de pays
     */
    @Column(length = 2, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String codeIso2 = "";

    /**
     * Code du pays sur 3 caractères conforme à la norme internationale ISO 3166 de représentation des noms de pays
     */
    @Column(length = 3, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String codeIso3 = "";

    /**
     * Code du pays à 3 chiffres conforme à la norme internationale ISO 3166 de représentation des noms de pays
     */
    @Column(length = 3, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String codeNum3 = "";

    /**
     * @param from
     * @param base
     * @return
     */
    @Transactional
    public static Pays updateFrom(Pays from, Pays base) {
        base.set__version(
            Optional.ofNullable(from.get__version()).orElse(base.get__version())
        );
        base.setLibelle(
            Optional.ofNullable(from.getLibelle()).orElse(base.getLibelle())
        );
        base.setLibelleEnrichi(
            Optional.ofNullable(from.getLibelleEnrichi()).orElse(
                base.getLibelleEnrichi()
            )
        );
        base.setAncienNom(
            Optional.ofNullable(from.getAncienNom()).orElse(base.getAncienNom())
        );
        base.setCodeIso2(
            Optional.ofNullable(from.getCodeIso2()).orElse(base.getCodeIso2())
        );
        base.setCodeIso3(
            Optional.ofNullable(from.getCodeIso3()).orElse(base.getCodeIso3())
        );
        base.setCodeNum3(
            Optional.ofNullable(from.getCodeNum3()).orElse(base.getCodeNum3())
        );

        return base;
    }
}
