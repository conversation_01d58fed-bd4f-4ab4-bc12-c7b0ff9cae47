/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Embeddable
@Access(AccessType.FIELD)
public class Adresse implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -5328396076009321291L;

    /**
     * Complément d’identification du destinataire ou point de remise à l’intérieur du bâtiment :
     * N° appartement, boite aux lettres, étage, couloir
     */
    @Column(name = "adr_complementDestinataire", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String complementDestinataire = "";

    /**
     * Complément d’identification du point géographique – extérieur du bâtiment :
     * entrée, tour, bâtiment, immeuble, résidence
     */
    @Column(name = "adr_complementGeographique", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String complementGeographique = "";

    /**
     * Numéro et libellé de la voie
     */
    @Column(name = "adr_libelle", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String libelle = "";

    /**
     *
     */
    @Column(name = "adr_complementLibelle", length = 200)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String complementLibelle = "";

    /**
     *
     */
    @Column(name = "adr_codePostal", length = 10)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String codePostal = "";

    /**
     *
     */
    @Column(name = "adr_communeLibelle", length = 150)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String communeLibelle = "";

    /**
     *
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "adr_commune_oid")
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected Commune commune;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "adr_pays_oid")
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected Pays pays;

    /**
     *
     */
    public Adresse() {}

    public void importAdresse(Adresse that) {
        if (that.libelle != null) {
            setLibelle(that.libelle);
        }

        if (that.complementLibelle != null) {
            setComplementLibelle(that.complementLibelle);
        }

        if (that.complementGeographique != null) {
            setComplementGeographique(that.complementGeographique);
        }

        if (that.complementDestinataire != null) {
            setComplementDestinataire(that.complementDestinataire);
        }

        if (that.codePostal != null) {
            setCodePostal(that.codePostal);
        }

        if (that.communeLibelle != null) {
            setCommuneLibelle(that.communeLibelle);
        }

        if (that.commune != null) {
            setCommune(that.commune);
        }

        if (that.pays != null) {
            setPays(that.pays);
        }
    }

    @Override
    public String toString() {
        return libelle + " " + codePostal + " " + communeLibelle;
    }
}
