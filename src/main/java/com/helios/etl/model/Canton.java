/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.util.Collection;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "cog_canton")
public class Canton extends CogEntity {

    /**
     *
     */
    private static final long serialVersionUID = -7250136487742681202L;

    /**
     * Code du canton
     */
    @Column(length = 10, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String code = "";

    /**
     *
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "canton")
    @Getter
    @Setter
    protected Collection<Commune> communes;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "arrondissement_oid", nullable = true)
    @Getter
    @Setter
    protected Arrondissement arrondissement;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "departement_oid", nullable = false)
    @Getter
    @Setter
    protected Departement departement;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_oid", nullable = false)
    @Getter
    @Setter
    protected Region region;

    /**
     *
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cheflieu_oid", nullable = true)
    @Getter
    @Setter
    protected Commune chefLieu;

    /**
     *
     */
    public Canton() {}

    /**
     */
    public static Canton updateFrom(Canton from, Canton base) {
        base.setCode(
            Optional.ofNullable(from.getCode()).orElse(base.getCode())
        );
        base.setCommunes(
            Optional.ofNullable(from.getCommunes()).orElse(base.getCommunes())
        );
        base.setArrondissement(
            Optional.ofNullable(from.getArrondissement()).orElse(
                base.getArrondissement()
            )
        );
        base.setDepartement(
            Optional.ofNullable(from.getDepartement()).orElse(
                base.getDepartement()
            )
        );
        base.setRegion(
            Optional.ofNullable(from.getRegion()).orElse(base.getRegion())
        );
        base.setChefLieu(
            Optional.ofNullable(from.getChefLieu()).orElse(base.getChefLieu())
        );
        base.set__version(
            Optional.ofNullable(from.get__version()).orElse(base.get__version())
        );
        base.setNom(Optional.ofNullable(from.getNom()).orElse(base.getNom()));
        base.setNomMaj(
            Optional.ofNullable(from.getNomMaj()).orElse(base.getNomMaj())
        );
        base.setArticle(
            Optional.ofNullable(from.getArticle()).orElse(base.getArticle())
        );
        base.setArticleMaj(
            Optional.ofNullable(from.getArticleMaj()).orElse(
                base.getArticleMaj()
            )
        );

        return base;
    }
}
