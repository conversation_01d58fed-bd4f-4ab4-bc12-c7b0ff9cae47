/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonTypeName;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

/**
 * Une intervention utilisée par la BU informatique
 * <AUTHOR>
 */
@Entity
@JsonTypeName("InterventionInformatique")
@DiscriminatorValue("InterventionInformatique")
public class InterventionInformatique extends AbstractIssue {

    /**
     * La nature de l'intervention
     */
    @ManyToOne
    @JoinColumn(name = "nature_intervention_oid", nullable = true)
    @Getter
    @Setter
    protected IssueNatureIntervention nature;

    /**
     * La modalité de l'intervention
     */
    @ManyToOne
    @JoinColumn(name = "modalite_intervention_oid", nullable = false)
    @Getter
    @Setter
    protected IssueModaliteIntervention modalite;
}
