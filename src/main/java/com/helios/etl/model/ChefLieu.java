/**
 *
 */
package com.helios.etl.model;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public enum ChefLieu {
    /**
     * commune non chef-lieu
     */
    NON(0),

    /**
     * commune bureau centralisateur de canton
     */
    BUREAU_CENTRALISATEUR(1),

    /**
     * commune chef-lieu d'arrondissement
     */
    ARRONDISSEMENT(2),

    /**
     * commune chef-lieu de département
     */
    DEPARTEMENT(3),

    /**
     * commune chef-lieu de région
     */
    REGION(4);

    /**
     *
     */
    @Getter
    private int value = 0;

    /**
     * @param value
     */
    private ChefLieu(int value) {
        this.value = value;
    }
}
