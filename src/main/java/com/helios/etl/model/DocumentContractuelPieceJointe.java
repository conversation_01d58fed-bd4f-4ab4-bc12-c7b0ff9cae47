/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.helios.etl.outer.DateTimeConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Une DocumentContractuelPieceJointe désigne un fichier associé à un DocumentContractuel,
 * permettant de conserver une version électronique, numérisée ou complémentaire d’un document formel.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_document_contractuel_piece_jointe")
public class DocumentContractuelPieceJointe {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * Nom lisible affiché dans l’interface utilisateur.
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    protected String libelle = "";

    /**
     * Texte explicatif plus détaillé, destiné à documenter les finalités, responsabilités et périmètre de cette pièce jointe
     */
    @Column(nullable = false, columnDefinition = "text")
    @Getter
    @Setter
    protected String description = "";

    /**
     *
     */
    @Column(nullable = false, length = 200)
    @Getter
    @Setter
    protected String fichier = "";

    /**
     * Type MIME (application/pdf, image/png…)
     */
    @Column(
        length = 255,
        nullable = false,
        columnDefinition = "varchar(255) default 'application/octet-stream'"
    )
    @Getter
    @Setter
    protected String typeMime = "application/octet-stream";

    /**
     * Taille du fichier en octets.
     */
    @Column(nullable = false, columnDefinition = "bigint unsigned default 0")
    @Getter
    @Setter
    protected long taille = 0;

    /**
     * Date de création de la pièce jointe
     */
    @Column(nullable = false)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateCreation = LocalDateTime.now();

    /**
     *
     */
    @Getter
    @Setter
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_oid", nullable = false)
    protected DocumentContractuel document;
}
