/**
 *
 */
package com.helios.etl.model;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@MappedSuperclass
public class CogEntity implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1171606202631552423L;

    /**
     *
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     *
     */
    @Getter
    @Setter
    @Version
    protected Integer __version = 1;

    /**
     * Nom (typographie riche)
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    protected String nom = "";

    /**
     * Nom (majuscules)
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    protected String nomMaj = "";

    /**
     * Article (typographie riche)
     */
    @Column(length = 10, nullable = false)
    @Getter
    @Setter
    protected String article = "";

    /**
     * Article (majuscules)
     */
    @Column(length = 10, nullable = false)
    @Getter
    @Setter
    protected String articleMaj = "";
}
