/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.helios.etl.outer.DateTimeConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Le Journal est l’historique structuré des événements associés à une Issue. Il enregistre chaque modification, action, commentaire ou interaction liée à l’issue, de façon chronologique et horodatée.
 */
@Entity
@Table(name = "hls_journal")
public class Journal {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * Commentaire ajouté
     */
    @Column(nullable = false, columnDefinition = "text")
    @Getter
    @Setter
    protected String note = "";

    /**
     * Date de création de ce journal
     */
    @Column(nullable = false)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateCreation = LocalDateTime.now();

    /**
     * Ce journal n'est visible que par son auteur
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean prive = false;

    /**
     * Ce journal est à usage strictement interne
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean interne = false;

    /**
     * Issue à laquelle est rattaché ce journal.
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "issue_oid", nullable = false)
    @Getter
    @Setter
    protected AbstractIssue issue;

    /**
     * Pièces-jointes associées à ce journal
     */
    @OneToMany(mappedBy = "journal")
    @Getter
    @Setter
    protected Set<IssuePieceJointe> piecesJointes = new HashSet<>();
}
