package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Collection;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(
    name = "cog_commune",
    indexes = {
        @Index(
            name = "index_clientNomMaj",
            columnList = "nomMaj",
            unique = false
        ),
    }
)
public class Commune extends CogEntity implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -7284431402839432556L;

    /**
     * Chef-lieu d'arrondissement, de département, de région ou bureau centralisateur de canton
     */
    @Enumerated(EnumType.ORDINAL)
    @Column(nullable = false, columnDefinition = "smallint")
    @Getter
    @Setter
    protected ChefLieu chefLieu = ChefLieu.NON;

    /**
     * Code de la commune sur 5 positions
     */
    @Column(length = 6, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String code = "";

    /**
     * Coordonnées GPS
     */
    @Column(length = 40, nullable = true)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String coordonneesGPS = "";

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "canton_oid", nullable = true)
    @Getter
    @Setter
    @JsonIgnore
    protected Canton canton;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "arrondissement_oid", nullable = true)
    @Getter
    @Setter
    @JsonIgnore
    protected Arrondissement arrondissement;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "departement_oid", nullable = false)
    @Getter
    @Setter
    @JsonProperty("departement")
    protected Departement departement;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_oid", nullable = false)
    @Getter
    @Setter
    @JsonProperty("region")
    protected Region region;

    /**
     *
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "commune")
    @Getter
    @Setter
    @JsonIgnore
    //@JsonView(FilterView.Public.class)
    protected Collection<CodePostal> codesPostaux;

    /**
     *
     */
    public Commune() {}

    /**
     *
     * @return
     */
    public String getCodeInsee() {
        return code;
    }

    /**
     * @param from
     * @param base
     * @return
     */
    @Transactional
    public Commune updateFrom(Commune from, Commune base) {
        base.set__version(
            Optional.ofNullable(from.get__version()).orElse(base.get__version())
        );
        base.setChefLieu(
            Optional.ofNullable(from.getChefLieu()).orElse(base.getChefLieu())
        );
        base.setCode(
            Optional.ofNullable(from.getCode()).orElse(base.getCode())
        );
        base.setCoordonneesGPS(
            Optional.ofNullable(from.getCoordonneesGPS()).orElse(
                base.getCoordonneesGPS()
            )
        );
        base.setCanton(
            Optional.ofNullable(from.getCanton()).orElse(base.getCanton())
        );
        base.setArrondissement(
            Optional.ofNullable(from.getArrondissement()).orElse(
                base.getArrondissement()
            )
        );
        base.setDepartement(
            Optional.ofNullable(from.getDepartement()).orElse(
                base.getDepartement()
            )
        );
        base.setRegion(
            Optional.ofNullable(from.getRegion()).orElse(base.getRegion())
        );
        base.setCodesPostaux(
            Optional.ofNullable(from.getCodesPostaux()).orElse(
                base.getCodesPostaux()
            )
        );
        base.setNom(Optional.ofNullable(from.getNom()).orElse(base.getNom()));
        base.setNomMaj(
            Optional.ofNullable(from.getNomMaj()).orElse(base.getNomMaj())
        );
        base.setArticle(
            Optional.ofNullable(from.getArticle()).orElse(base.getArticle())
        );
        base.setArticleMaj(
            Optional.ofNullable(from.getArticleMaj()).orElse(
                base.getArticleMaj()
            )
        );

        return base;
    }
}
