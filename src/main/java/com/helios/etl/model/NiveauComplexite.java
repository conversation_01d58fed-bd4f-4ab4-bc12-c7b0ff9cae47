/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;

/**
 * Un NiveauComplexite représente un palier d’évaluation de la difficulté ou de la technicité d’une Issue.
 * Il permet de qualifier l’effort estimé, les compétences requises, et parfois la nécessité d’escalade fonctionnelle ou technique.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_niveau_complexite")
public class NiveauComplexite extends AbstractListeValeurs {

    /**
     *
     */
    private static final long serialVersionUID = -7572724272998466991L;

    /**
     * Lien avec le domaine
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_mission_type_has_domaine_metier",
        joinColumns = @JoinColumn(
            name = "mission_type_oid",
            referencedColumnName = "oid"
        ),
        inverseJoinColumns = @JoinColumn(
            name = "domaine_metier_oid",
            referencedColumnName = "oid"
        )
    )
    @Getter
    @Setter
    @JsonIgnore
    protected Collection<DomaineMetier> domainesMetier;
}
