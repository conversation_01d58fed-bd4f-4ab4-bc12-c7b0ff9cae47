/**
 *
 */
package com.helios.etl.model;

import com.helios.etl.outer.spring.hibernate.PersistentLiteralEnum;

/**
 * <AUTHOR>
 *
 */
public enum TypeRelationIssue implements PersistentLiteralEnum {
    /**
     * Relation non directionnelle indiquant que deux issues sont liées par un contexte fonctionnel commun
     * sans dépendance hiérarchique ni contrainte temporelle. Utilisé pour marquer des sujets similaires,
     * associés ou interdépendants de manière souple.
     */
    RELIE_A("relie_a"),

    /**
     * Cette issue bloque l’issue cible : celle-ci ne peut pas être commencée ou poursuivie tant que la source n’est pas terminée.
     * Relation de dépendance forte.
     */
    BLOQUE("bloque"),

    /**
     * Cette issue est bloquée par l’issue source : elle ne peut pas avancer tant que l’autre n’est pas traitée.
     * C’est l’inverse logique de BLOQUE
     */
    BLOQUE_PAR("bloque_par"),

    /**
     * Cette issue est un duplicata fonctionnel de l’issue cible. Les deux couvrent la même problématique, mais celle-ci a été signalée à nouveau.
     * Généralement, une seule est conservée comme référence.
     */
    DUPLIQUE("duplique"),

    /**
     * Cette issue a été dupliquée par une autre : l’issue source est une re-soumission du même problème. Relation inverse de DUPLIQUE
     */
    DUPLIQUE_PAR("duplique_par"),

    /**
     * Cette issue doit être réalisée avant l’issue cible. Relation directionnelle et temporelle (souvent associée à un délai de transition).
     */
    PRECEDE("precede"),

    /**
     * Cette issue doit être réalisée après l’issue source. C’est l’inverse de PRECEDE. Peut inclure un délai exprimé en jours entre les deux.
     */
    SUIT("suit"),

    /**
     * L’issue source fait référence à l’issue cible comme élément de contexte, d’historique ou d'information utile, mais sans relation de dépendance directe.
     */
    REFERE_A("refere_a"),

    /**
     * L’issue source remplace l’issue cible, c’est-à-dire qu’elle prend officiellement le relais du traitement ou de la résolution d’un besoin précédemment exprimé dans une autre issue.
     */
    REMPLACE("remplace");

    /**
     *
     */
    private String type = "relie_a";

    /**
     * @param type
     */
    private TypeRelationIssue(String type) {
        this.type = type;
    }

    /* (non-Javadoc)
     * @see fr.actuelburo.spring.hibernate.PersistentEnum#getId()
     */
    @Override
    public String getId() {
        return type;
    }
}
