/**
 *
 */
package com.helios.etl.model;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

/**
 * Une Mission désigne une activité métier cohérente, continue ou récurrente, portée par une équipe ou un domaine d’expertise,
 * ayant pour but de répondre à un objectif fonctionnel stable au service de l’entreprise ou de ses clients.
 * <AUTHOR>
 */
@Entity
@DiscriminatorValue("Mission")
public class Mission extends AbstractActivite {

    /**
     * catégorie fonctionnelle de la mission, c’est-à-dire sa finalité opérationnelle
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "mission_type_oid", nullable = true)
    @Getter
    @Setter
    protected TypeMission type;
}
