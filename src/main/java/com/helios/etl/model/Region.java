/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.util.Collection;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "cog_region")
public class Region extends CogEntity {

    /**
     *
     */
    private static final long serialVersionUID = 6880210347679391148L;

    /**
     * Code de la région
     */
    @Column(length = 10, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String code = "";

    /**
     *
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "region")
    @Getter
    @Setter
    protected Collection<Commune> communes;

    /**
     *
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "region")
    @Getter
    @Setter
    protected Collection<Canton> cantons;

    /**
     *
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "region")
    @Getter
    @Setter
    protected Collection<Arrondissement> arrondissements;

    /**
     *
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "region")
    @Getter
    @Setter
    protected Collection<Departement> departements;

    /**
     *
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cheflieu_oid", nullable = true)
    @Getter
    @Setter
    protected Commune chefLieu;

    /**
     *
     */
    public Region() {}

    /**
     * @param base
     * @param from
     */
    public static Region updateFrom(Region from, Region base) {
        base.setNom(Optional.ofNullable(from.getNom()).orElse(base.getNom()));
        base.setNomMaj(
            Optional.ofNullable(from.getNomMaj()).orElse(base.getNomMaj())
        );
        base.setArticle(
            Optional.ofNullable(from.getArticle()).orElse(base.getArticle())
        );
        base.setArticleMaj(
            Optional.ofNullable(from.getArticleMaj()).orElse(
                base.getArticleMaj()
            )
        );
        base.set__version(
            Optional.ofNullable(from.get__version()).orElse(base.get__version())
        );
        base.setCode(
            Optional.ofNullable(from.getCode()).orElse(base.getCode())
        );
        base.setChefLieu(
            Optional.ofNullable(from.getChefLieu()).orElse(base.getChefLieu())
        );
        base.setCommunes(
            Optional.ofNullable(from.getCommunes()).orElse(base.getCommunes())
        );
        base.setCantons(
            Optional.ofNullable(from.getCantons()).orElse(base.getCantons())
        );
        base.setArrondissements(
            Optional.ofNullable(from.getArrondissements()).orElse(
                base.getArrondissements()
            )
        );
        base.setDepartements(
            Optional.ofNullable(from.getDepartements()).orElse(
                base.getDepartements()
            )
        );

        return base;
    }
}
