/**
 *
 * <AUTHOR>
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

/**
 *
 */
@Entity
@Table(name = "hls_issue_modalite_intervention")
public class IssueModaliteIntervention {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * La personne en contact pour cette intervention
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "contact_oid", nullable = true)
    @Getter
    @Setter
    protected Personne contact;

    /**
     * Indique si l'intervention se fait à distance
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean distanciel = false;

    /**
     * Indique si l'intervention se fait en présence
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean presentiel = false;

    /**
     * L'adresse de l'intervention
     */
    @Embedded
    @Getter
    @Setter
    protected Adresse adresse;
}
