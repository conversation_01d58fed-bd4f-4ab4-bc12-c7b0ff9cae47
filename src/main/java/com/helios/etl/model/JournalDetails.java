/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;

/**
 *
 */
@Entity
@Table(name = "hls_journal_details")
public class JournalDetails {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     *
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "journal_oid", nullable = false)
    @Getter
    @Setter
    private Journal journal;

    /**
     * Type de propriété modifiée (par exemple, 'attr' pour un attribut).
     */
    @Column(nullable = false, length = 100)
    @Getter
    @Setter
    private String proprieteType;

    /**
     * Nom de la propriété modifiée
     */
    @Column(nullable = false, length = 100)
    @Getter
    @Setter
    private String propriete;

    /**
     * Ancienne valeur
     */
    @Column(columnDefinition = "text")
    @Getter
    @Setter
    private String ancienneValeur;

    /**
     * Nouvelle valeur
     */
    @Column(columnDefinition = "text")
    @Getter
    @Setter
    private String nouvelleValeur;
}
