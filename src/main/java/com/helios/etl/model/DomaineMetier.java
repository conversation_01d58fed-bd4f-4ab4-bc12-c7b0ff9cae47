/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.helios.etl.outer.DateTimeConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Un domaine métier est un ensemble cohérent d’activités, de savoir-faire, de processus, de règles et de responsabilités au sein d’une organisation,
 * structuré autour d’un objectif fonctionnel commun et souvent confié à une équipe spécialisée. Il représente une vue métier de l’entreprise,
 * distincte de sa structuration technique ou projet.
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "hls_domaine_metier")
public class DomaineMetier {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * Code fonctionnel court et unique, utilisé pour les intégrations, exports ou l’identification rapide.
     */
    @Column(length = 50, unique = true, nullable = false)
    @Getter
    @Setter
    protected String code = "";

    /**
     * Nom lisible affiché dans l’interface utilisateur.
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    protected String libelle = "";

    /**
     * Texte explicatif plus détaillé, destiné à documenter les finalités, responsabilités et périmètre du domain
     */
    @Column(nullable = false, columnDefinition = "text")
    @Getter
    @Setter
    protected String description = "";

    /**
     *
     */
    @Column(nullable = false, columnDefinition = "bit(1) default 0")
    @Getter
    @Setter
    protected boolean obsolete = false;

    /**
     *
     */
    @Column(nullable = false)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateCreation = LocalDateTime.now();

    /**
     *
     */
    public DomaineMetier() {}
}
