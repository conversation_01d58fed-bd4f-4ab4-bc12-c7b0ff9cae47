/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.helios.etl.outer.spring.jackson.FilterView;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(
    name = "cog_code_postal",
    indexes = {
        @Index(name = "index_codePostal", columnList = "code", unique = false),
    }
)
public class CodePostal {

    /**
     *
     */
    @Id
    @Column(length = 5, unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     *
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     *
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String libelle = "";

    /**
     * Code postal
     */
    @Column(length = 6, nullable = false)
    @Getter
    @Setter
    @JsonView(FilterView.Public.class)
    protected String code = "";

    /**
     *
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "commune_oid", nullable = true)
    @Getter
    @Setter
    protected Commune commune;

    /**
     *
     */
    public CodePostal() {}

    /**
     * @param from
     * @param base
     * @return
     */
    @Transactional
    public CodePostal updateFrom(CodePostal from, CodePostal base) {
        base.set__version(
            Optional.ofNullable(from.get__version()).orElse(base.get__version())
        );
        base.setLibelle(
            Optional.ofNullable(from.getLibelle()).orElse(base.getLibelle())
        );
        base.setCode(
            Optional.ofNullable(from.getCode()).orElse(base.getCode())
        );
        base.setCommune(
            Optional.ofNullable(from.getCommune()).orElse(base.getCommune())
        );

        return base;
    }
}
