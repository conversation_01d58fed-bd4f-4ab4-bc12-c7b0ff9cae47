/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "hls_mission_type")
public class TypeMission extends AbstractListeValeurs {

    /**
     *
     */
    private static final long serialVersionUID = 1937972085764262383L;

    /**
     * Lien avec le domaine
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_mission_type_has_domaine_metier",
        joinColumns = @JoinColumn(
            name = "mission_type_oid",
            referencedColumnName = "oid"
        ),
        inverseJoinColumns = @JoinColumn(
            name = "domaine_metier_oid",
            referencedColumnName = "oid"
        )
    )
    @Getter
    @Setter
    @JsonIgnore
    protected Collection<DomaineMetier> domainesMetier;
}
