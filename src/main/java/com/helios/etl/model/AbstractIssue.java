/**
 *
 */
package com.helios.etl.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.helios.etl.outer.DateTimeConstants;
import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Une Issue abstraite représentant toute unité de gestion opérationnelle représentant un événement, une demande, une anomalie, une action
 * ou une intervention, structurée pour être suivie, qualifiée, traitée et clôturée dans le cadre d’une mission d’un domaine métier
 * de l’entreprise.
 *
 * <AUTHOR>
 */

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "__type",
    visible = true
)
@JsonSubTypes(
    {
        @JsonSubTypes.Type(
            value = IssueInformatique.class,
            name = "IssueInformatique"
        ),
        @JsonSubTypes.Type(
            value = InterventionInformatique.class,
            name = "InterventionInformatique"
        ),
    }
)
@Entity
@Table(name = "hls_issue")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "__type")
public abstract class AbstractIssue {

    /**
     * Identifiant unique dans la base de données
     */
    @Id
    @Column(unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Getter
    @Setter
    protected long oid;

    /**
     * Version technique de l’objet, utilisée pour le contrôle d’accès concurrentiel ou l’optimistic lockin
     */
    @Getter
    @Setter
    @Version
    @JsonIgnore
    protected Integer __version;

    /**
     * Code fonctionnel court et unique, utilisé pour les intégrations, exports ou l’identification rapide.
     */
    @Column(length = 50, unique = true, nullable = false)
    @Getter
    @Setter
    protected String code = "";

    /**
     * Nom lisible affiché dans l’interface utilisateur.
     */
    @Column(length = 200, nullable = false)
    @Getter
    @Setter
    protected String sujet = "";

    /**
     * Texte explicatif plus détaillé, destiné à documenter les finalités, responsabilités et périmètre de l'issue
     */
    @Column(nullable = false, columnDefinition = "mediumtext")
    @Getter
    @Setter
    protected String description = "";

    /**
     * Date de création de l'issue
     */
    @Column(nullable = false)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateCreation = LocalDateTime.now();

    /**
     * Date de modification de l'issue
     */
    @Column(nullable = false)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateModification = LocalDateTime.now();

    /**
     * Date prévisionnelle de début de l'issue
     */
    @Column(nullable = true)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime datePrevisionnelleDebut = null;

    /**
     * Date prévisionnelle de fin de l'issue
     */
    @Column(nullable = true)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime datePrevisionnelleFin = null;

    /**
     * Date effective de début de l'issue
     */
    @Column(nullable = true)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateEffectiveDebut = null;

    /**
     * Date effective de fin de l'issue
     */
    @Column(nullable = true)
    @DateTimeFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @JsonFormat(pattern = DateTimeConstants.FORMAT_DATETIME)
    @Getter
    @Setter
    protected LocalDateTime dateEffectiveFin = null;

    /**
     * La priorité affectée à cette issue
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "priorite_oid", nullable = false)
    @Getter
    @Setter
    protected IssuePriorite priorite;

    /**
     * Le statut affecté à cette issue
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "statut_oid", nullable = false)
    @Getter
    @Setter
    protected IssueStatut statut;

    /**
     * Activité à laquelle cette issue est rattachée (Projet ou Mission).
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "activite_oid", nullable = false)
    @Getter
    @Setter
    protected AbstractActivite activite;

    /**
     * Type java de l'activité associée
     */
    @Column(length = 255, nullable = false)
    @Getter
    @Setter
    protected String kindOfActivite = "";

    /**
     * Issue parente (facultative) de cette issue.
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "issue_parente_oid")
    @Getter
    @Setter
    protected AbstractIssue issueParente;

    /**
     * Type java de l'issue associée
     */
    @Column(length = 255, nullable = false)
    @Getter
    @Setter
    protected String kindOfIssueParente = "";

    /**
     * Sous-issues (enfants) de cette issue.
     */
    @OneToMany(mappedBy = "issueParente")
    @Getter
    @Setter
    protected Set<AbstractIssue> issues = new HashSet<>();

    /**
     * niveau d’exécution estimé d’une issue sous forme de pourcentage entier, compris entre 0 (non commencé) et 100 (entièrement terminé).
     */
    @Column(nullable = false, columnDefinition = "tinyint unsigned default 0")
    @Min(0)
    @Max(100)
    @Getter
    @Setter
    protected byte avancement;

    /**
     * Représente la durée estimée en minutes nécessaire pour traiter cette issue
     */
    @Column(nullable = false, columnDefinition = "int unsigned default 0")
    @Getter
    @Setter
    protected int tempsEstimeMinutes;

    /**
     * Représente la durée effective en minutes nécessaire pour traiter cette issue
     */
    @Column(nullable = false, columnDefinition = "int unsigned default 0")
    @Getter
    @Setter
    protected int tempsEffectifMinutes;

    /**
     * Relations sortantes (cette issue est la source).
     */
    @OneToMany(mappedBy = "source")
    @Getter
    @Setter
    protected Set<IssueRelation> relationsSortantes = new HashSet<>();

    /**
     * Relations entrantes (cette issue est la cible).
     */
    @OneToMany(mappedBy = "cible")
    @Getter
    @Setter
    protected Set<IssueRelation> relationsEntrantes = new HashSet<>();

    /**
     * Journal des modifications ou commentaires liés à cette issue.
     */
    @OneToMany(mappedBy = "issue")
    @Getter
    @Setter
    protected Set<Journal> journaux = new HashSet<>();

    /**
     * L'origine de l'issue
     */
    @ManyToOne
    @JoinColumn(name = "origine_oid", nullable = false)
    @Getter
    @Setter
    protected IssueOrigine origine;

    /**
     * Le commanditaire associé cette issue
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "commanditaire_oid", nullable = false)
    @Getter
    @Setter
    protected Commanditaire commanditaire;

    /**
     * La personne à l'origine de l'issue
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "demandeur_oid", nullable = false)
    @Getter
    @Setter
    protected Personne demandeur;

    /**
     * La personne ayant rédigé l'issue
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "redacteur_oid", nullable = false)
    @Getter
    @Setter
    protected Personne redacteur;

    /**
     * L'intervenant affecté à cette issue
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "intervenant_principal_oid", nullable = false)
    @Getter
    @Setter
    protected Personne intervenantPrincipal;

    /**
     * Les intervenants concernés pas cette issue en plus de l'intervenant principal
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_issue_has_intervenant",
        joinColumns = @JoinColumn(
            name = "issue_oid",
            referencedColumnName = "oid"
        ),
        inverseJoinColumns = @JoinColumn(
            name = "personne_oid",
            referencedColumnName = "oid"
        )
    )
    @Getter
    @Setter
    protected Set<Personne> intervenants = new HashSet<>();

    /**
     * Liste des personnes observatrices de cette issue.
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_issue_observateur",
        joinColumns = @JoinColumn(name = "issue_oid"),
        inverseJoinColumns = @JoinColumn(name = "personne_oid")
    )
    @Getter
    @Setter
    protected Set<Personne> observateurs = new HashSet<>();

    /**
     * Pièces-jointes associées à cette issue
     */
    @OneToMany(mappedBy = "issue")
    @Getter
    @Setter
    protected Set<IssuePieceJointe> piecesJointes = new HashSet<>();

    /**
     * Documents contractuels associés à cette issue
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "hls_issue_has_document_contractuel",
        joinColumns = @JoinColumn(name = "issue_oid"),
        inverseJoinColumns = @JoinColumn(name = "document_oid")
    )
    @Getter
    @Setter
    protected Set<DocumentContractuel> documentsContractuels = new HashSet<>();

    /**
     * Options pour cette isue
     */
    @Type(JsonStringType.class)
    @Column(columnDefinition = "json", nullable = true)
    @Getter
    @Setter
    protected Map<String, String> options = null;

    /**
     * Retourne le temps estimé de traitement sous forme de Duration.
     *
     * @return une Duration équivalente
     */
    public Duration toTempsEstimeDuration() {
        return Duration.ofMinutes(tempsEstimeMinutes);
    }

    /**
     * Retourne le temps effectif de traitement sous forme de Duration.
     *
     * @return une Duration équivalente
     */
    public Duration toTempsEffectifDuration() {
        return Duration.ofMinutes(tempsEffectifMinutes);
    }
}
