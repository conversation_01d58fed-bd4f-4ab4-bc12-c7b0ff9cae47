package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.DocumentContractuel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DocumentContractuelRepository
    extends JpaRepository<DocumentContractuel, Long> {
    /**
     * Recherche un(e) DocumentContractuel par son OID
     */
    Optional<DocumentContractuel> findOneByOid(long oid);
}
