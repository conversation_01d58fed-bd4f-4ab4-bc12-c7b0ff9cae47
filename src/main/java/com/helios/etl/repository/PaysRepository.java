/**
 *
 */
package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.Pays;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface PaysRepository extends JpaRepository<Pays, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<Pays> findOneByOid(Long oid);

    /**
     * @return
     */
    List<Pays> findAllByOrderByLibelle();

    /**
     * @param code
     * @return
     */
    Optional<Pays> findOneByCodeIso2(String code);

    /**
     * @param code
     * @return
     */
    Optional<Pays> findOneByCodeIso3(String code);

    /**
     * @return
     */
    boolean existsOneByCodeIso2(String code);

    /**
     * @return
     */
    boolean existsOneByCodeIso3(String code);

    /**
     * @param pays
     * @return
     */
    Optional<Pays> findOneByLibelle(String pays);
}
