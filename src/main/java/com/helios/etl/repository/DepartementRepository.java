/**
 *
 */
package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Departement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface DepartementRepository
    extends JpaRepository<Departement, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<Departement> findOneByOid(Long oid);
}
