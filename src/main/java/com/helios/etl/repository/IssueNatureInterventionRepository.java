package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueNatureIntervention;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueNatureInterventionRepository
    extends JpaRepository<IssueNatureIntervention, Long> {
    /**
     * Recherche un(e) IssueNatureIntervention par son OID
     */
    Optional<IssueNatureIntervention> findOneByOid(long oid);
}
