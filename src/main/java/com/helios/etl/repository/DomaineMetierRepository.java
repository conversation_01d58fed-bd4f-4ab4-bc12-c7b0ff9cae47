package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.DomaineMetier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DomaineMetierRepository
    extends JpaRepository<DomaineMetier, Long> {
    /**
     * Recherche un(e) DomaineMetier par son OID
     */
    Optional<DomaineMetier> findOneByOid(long oid);
}
