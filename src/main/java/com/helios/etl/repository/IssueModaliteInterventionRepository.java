package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueModaliteIntervention;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueModaliteInterventionRepository
    extends JpaRepository<IssueModaliteIntervention, Long> {
    /**
     * Recherche un(e) IssueNatureIntervention par son OID
     */
    Optional<IssueModaliteIntervention> findOneByOid(long oid);
}
