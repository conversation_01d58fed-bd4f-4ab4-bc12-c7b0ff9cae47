/**
 *
 */
package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.CodePostal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface CodePostalRepository extends JpaRepository<CodePostal, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<CodePostal> findOneByOid(Long oid);

    /**
     * @param code
     * @return
     */
    @Query(
        "Select c from CodePostal c where c.code like :paramCode% order by c.commune.nomMaj"
    )
    List<CodePostal> findAllByCodeStartingBy(@Param("paramCode") String code);

    /**
     * @param code
     * @return
     */
    @Query(
        "select c from CodePostal c " +
        "left join c.commune com " +
        "where c.code like :paramCode% " +
        "order by coalesce(com.nomMaj,c.libelle) "
    )
    List<CodePostal> findAllByCodeStartingByWithCommuneNull(
        @Param("paramCode") String code
    );

    /**
     * @param nomCommune
     * @return
     */
    @Query(
        "Select c from CodePostal c where c.commune.nomMaj like CONCAT(UPPER(:paramNomCommune),'%') order by c.commune.nom"
    )
    List<CodePostal> findAllByNomCommuneStartingBy(
        @Param("paramNomCommune") String nomCommune
    );

    /**
     * @return
     */
    Optional<CodePostal> findOneByLibelle(String libelle);

    /**
     * @param code
     * @param libelle
     * @return
     */
    Optional<CodePostal> findOneByLibelleAndCode(String libelle, String code);
}
