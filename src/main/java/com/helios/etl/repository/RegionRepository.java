/**
 *
 */
package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Region;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface RegionRepository extends JpaRepository<Region, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<Region> findOneByOid(Long oid);
}
