package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.DocumentContractuelPieceJointe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DocumentContractuelPieceJointeRepository
    extends JpaRepository<DocumentContractuelPieceJointe, Long> {
    /**
     * Recherche un(e) DocumentContractuelPieceJointe par son OID
     */
    Optional<DocumentContractuelPieceJointe> findOneByOid(long oid);
}
