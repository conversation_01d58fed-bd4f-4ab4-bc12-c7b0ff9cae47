package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.InterventionInformatique;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface InterventionInformatiqueRepository
    extends JpaRepository<InterventionInformatique, Long> {
    /**
     * Recherche un(e) InterventionInformatique par son OID
     */
    Optional<InterventionInformatique> findOneByOid(long oid);
}
