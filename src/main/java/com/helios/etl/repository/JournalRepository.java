package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.Journal;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JournalRepository extends JpaRepository<Journal, Long> {
    /**
     * Recherche un(e) Journal par son OID
     */
    Optional<Journal> findOneByOid(long oid);

    /**
     * Recherche un(e) Journal par son OID avec les Issues chargées
     *
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct j
        from Journal j
        left join fetch j.issue
        left join fetch j.piecesJointes
        where j.oid = :oid
        """
    )
    Optional<Journal> findOneByOidWithIssues(long oid);

    /**
     * Recherche tous les Journals avec les Issues chargées
     *
     * @return
     */
    @Query(
        """
        select distinct j
        from Journal j
        left join fetch j.issue
        left join fetch j.piecesJointes
        """
    )
    List<Journal> findAllWithIssues();

    /**
     * Recherche tous les Journals avec les Issues chargées
     *
     * @return
     */
    @Query(
        value = """
        select distinct j
        from Journal j
        left join fetch j.issue
        left join fetch j.piecesJointes
        """,
        countQuery = "select count(j) from Journal j"
    )
    Page<Journal> findAllWithIssues(Pageable pageable);
}
