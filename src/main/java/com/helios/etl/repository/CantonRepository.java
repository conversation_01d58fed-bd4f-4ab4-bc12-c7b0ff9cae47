/**
 *
 */
package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Canton;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface CantonRepository extends JpaRepository<Canton, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<Canton> findOneByOid(Long oid);
}
