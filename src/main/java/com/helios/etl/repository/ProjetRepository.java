package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.Projet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ProjetRepository extends JpaRepository<Projet, Long> {
    /**
     * Recherche un(e) Projet par son OID
     */
    Optional<Projet> findOneByOid(long oid);

    /**
     * Recherche un(e) Projet par son OID avec les détails chargés
     *
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct p
        from Projet p
        left join fetch p.domaines
        where p.oid = :oid
        """
    )
    Optional<Projet> findOneByOidWithDomaines(long oid);

    /**
     * Recherche tous les Projets avec les détails chargés
     *
     * @return
     */
    @Query(
        """
        select distinct p
        from Projet p
        left join fetch p.domaines
        """
    )
    List<Projet> findAllWithDomaines();

    /**
     * Recherche tous les Projets avec les détails chargés
     *
     * @return
     */
    @Query(
        value = """
        select distinct p
        from Projet p
        left join fetch p.domaines
        """,
        countQuery = "select count(p) from Projet p"
    )
    Page<Projet> findAllWithDomaines(Pageable pageable);
}
