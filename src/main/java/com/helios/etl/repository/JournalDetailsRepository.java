package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.JournalDetails;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface JournalDetailsRepository
    extends JpaRepository<JournalDetails, Long> {
    /**
     * Recherche un(e) JournalDetails par son OID
     */
    Optional<JournalDetails> findOneByOid(long oid);

    /**
     * Recherche un(e) JournalDetails par son OID avec les détails chargés
     *
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct j
        from JournalDetails j
        left join fetch j.journal
        where j.oid = :oid
        """
    )
    Optional<JournalDetails> findOneByOidWithJournal(long oid);

    /**
     * Recherche tous les JournalDetails avec les détails chargés
     *
     * @return
     */
    @Query(
        """
        select distinct j
        from JournalDetails j
        left join fetch j.journal
        """
    )
    List<JournalDetails> findAllWithJournal();

    /**
     * Recherche tous les JournalDetails avec les détails chargés
     *
     * @return
     */
    @Query(
        value = """
        select distinct j
        from JournalDetails j
        left join fetch j.journal
        """,
        countQuery = "select count(j) from JournalDetails j"
    )
    Page<JournalDetails> findAllWithJournal(Pageable pageable);
}
