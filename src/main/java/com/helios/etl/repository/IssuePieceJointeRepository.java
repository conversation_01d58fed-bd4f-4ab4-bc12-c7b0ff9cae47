package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.IssuePieceJointe;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface IssuePieceJointeRepository
    extends JpaRepository<IssuePieceJointe, Long> {
    /**
     * Recherche un(e) IssuePieceJointe par son OID
     */
    Optional<IssuePieceJointe> findOneByOid(long oid);

    /**
     * Recherche un(e) IssuePieceJointe par son OID avec l'Issue et le Journal
     * chargés
     *
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct i
        from IssuePieceJointe i
        left join fetch i.issue
        left join fetch i.journal
        where i.oid = :oid
        """
    )
    Optional<IssuePieceJointe> findOneByOidWithIssueAndJournal(long oid);

    /**
     * Recherche tous les IssuePieceJointe avec l'Issue et le Journal chargés
     *
     * @return
     */
    @Query(
        """
        select distinct i
        from IssuePieceJointe i
        left join fetch i.issue
        left join fetch i.journal
        """
    )
    List<IssuePieceJointe> findAllWithIssueAndJournal();

    /**
     * Recherche tous les IssuePieceJointe avec l'Issue et le Journal chargés
     *
     * @return
     */
    @Override
    @EntityGraph(attributePaths = { "issue", "journal" })
    List<IssuePieceJointe> findAll();

    /**
     * Recherche tous les IssuePieceJointe avec l'Issue et le Journal chargés
     *
     * @param pageable
     * @return
     */
    @Query(
        value = """
        select distinct i
        from IssuePieceJointe i
        left join fetch i.issue
        left join fetch i.journal
        """,
        countQuery = "select count(i) from IssuePieceJointe i"
    )
    Page<IssuePieceJointe> findAllWithIssueAndJournal(Pageable pageable);

    /**
     * Recherche tous les IssuePieceJointe avec l'Issue et le Journal chargés
     *
     * @param pageable
     * @return
     */
    @Override
    @EntityGraph(attributePaths = { "issue", "journal" })
    Page<IssuePieceJointe> findAll(Pageable pageable);
}
