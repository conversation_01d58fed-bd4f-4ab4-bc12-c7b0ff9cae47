package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.IssueRelation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueRelationRepository
    extends JpaRepository<IssueRelation, Long> {
    /**
     * Recherche un(e) IssueRelation par son OID
     */
    Optional<IssueRelation> findOneByOid(long oid);

    /**
     * Recherche un(e) IssueRelation par son OID avec les Issues chargées
     *
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct ir
        from IssueRelation ir
        left join fetch ir.source
        left join fetch ir.cible
        where ir.oid = :oid
        """
    )
    Optional<IssueRelation> findOneByOidWithIssues(long oid);

    /**
     * Recherche tous les IssueRelation avec les Issues chargées
     *
     * @return
     */
    @Query(
        """
                 select distinct ir
                 from IssueRelation ir
        left join fetch ir.source
        left join fetch ir.cible
                 """
    )
    List<IssueRelation> findAllWithIssues();

    /**
     * Recherche tous les IssueRelation avec les Issues chargées
     *
     * @return
     */
    @Query(
        value = """
                 select distinct ir
                 from IssueRelation ir
        left join fetch ir.source
        left join fetch ir.cible
                 """,
        countQuery = "select count(ir) from IssueRelation ir"
    )
    Page<IssueRelation> findAllWithIssues(Pageable pageable);
}
