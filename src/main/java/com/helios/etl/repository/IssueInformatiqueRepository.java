package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueInformatique;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueInformatiqueRepository
    extends JpaRepository<IssueInformatique, Long> {
    /**
     * Recherche un(e) IssueInformatique par son OID
     */
    @EntityGraph(attributePaths = "activite")
    Optional<IssueInformatique> findOneByOid(long oid);

    /**
     * Recherche un(e) IssueInformatique par son OID avec l'Activité chargée
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct i
        from IssueInformatique i
        left join fetch i.activite
        left join fetch i.issueParente
        where i.oid = :oid
        """
    )
    Optional<IssueInformatique> findOneByOidWithActivite(long oid);
}
