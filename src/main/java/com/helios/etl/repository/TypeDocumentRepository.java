package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.TypeDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TypeDocumentRepository
    extends JpaRepository<TypeDocument, Long> {
    /**
     * Recherche un(e) TypeDocument par son OID
     */
    Optional<TypeDocument> findOneByOid(long oid);
}
