/**
 *
 */
package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.CodePostal;
import com.helios.etl.model.Commune;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface CommuneRepository extends JpaRepository<Commune, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<Commune> findOneByOid(Long oid);

    /**
     * @return
     */
    List<Commune> findAllByOrderByNom();

    /**
     * @param nom
     * @return
     */
    List<Commune> findAllByNomMaj(String nom);

    /**
     * @return
     */
    Optional<Commune> findOneByCodesPostaux(CodePostal codePostal);
}
