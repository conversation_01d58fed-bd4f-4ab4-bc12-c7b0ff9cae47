/**
 *
 */
package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.Arrondissement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface ArrondissementRepository
    extends JpaRepository<Arrondissement, Long> {
    /**
     * @param oid
     * @return
     */
    Optional<Arrondissement> findOneByOid(Long oid);
}
