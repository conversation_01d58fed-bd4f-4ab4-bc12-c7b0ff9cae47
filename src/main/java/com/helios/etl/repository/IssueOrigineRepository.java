package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssueOrigine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssueOrigineRepository
    extends JpaRepository<IssueOrigine, Long> {
    /**
     * Recherche un(e) IssueOrigine par son OID
     */
    Optional<IssueOrigine> findOneByOid(long oid);

    Optional<IssueOrigine> findByLibelle(String heliosV1);
}
