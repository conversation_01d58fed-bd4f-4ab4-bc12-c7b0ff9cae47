package com.helios.etl.repository;

import java.util.Optional;

import com.helios.etl.model.IssuePriorite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IssuePrioriteRepository
    extends JpaRepository<IssuePriorite, Long> {
    /**
     * Recherche un(e) IssuePriorite par son OID
     */
    Optional<IssuePriorite> findOneByOid(long oid);
}
