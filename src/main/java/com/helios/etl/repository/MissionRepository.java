package com.helios.etl.repository;

import java.util.List;
import java.util.Optional;

import com.helios.etl.model.Mission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MissionRepository extends JpaRepository<Mission, Long> {
    /**
     * Recherche un(e) Mission par son OID
     */
    Optional<Mission> findOneByOid(long oid);

    /**
     * Recherche un(e) Mission par son OID avec les détails chargés
     *
     * @param oid
     * @return
     */
    @Query(
        """
        select distinct m
        from Mission m
        left join fetch m.domaines
        where m.oid = :oid
        """
    )
    Optional<Mission> findOneByOidWithDomaines(long oid);

    /**
     * Recherche tous les Missions avec les détails chargés
     *
     * @return
     */
    @Query(
        """
        select distinct m
        from Mission m
        left join fetch m.domaines
        """
    )
    List<Mission> findAllWithDomaines();

    /**
     * Recherche tous les Missions avec les détails chargés
     *
     * @return
     */
    @Query(
        value = """
        select distinct m
        from Mission m
        left join fetch m.domaines
        """,
        countQuery = "select count(m) from Mission m"
    )
    Page<Mission> findAllWithDomaines(Pageable pageable);
}
