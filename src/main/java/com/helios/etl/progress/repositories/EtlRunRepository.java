package com.helios.etl.progress.repositories;

import com.helios.etl.progress.entities.EtlRun;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EtlRunRepository extends JpaRepository<EtlRun, Long> {
    
    /**
     * Find ETL run by run ID
     */
    Optional<EtlRun> findByRunId(String runId);
    
    /**
     * Find the most recent ETL run
     */
    Optional<EtlRun> findTopByOrderByStartTimeDesc();
    
    /**
     * Find the most recent completed ETL run
     */
    Optional<EtlRun> findTopByStatusOrderByStartTimeDesc(EtlRun.EtlRunStatus status);
    
    /**
     * Find all ETL runs within a date range
     */
    List<EtlRun> findByStartTimeBetweenOrderByStartTimeDesc(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find all running ETL runs (should typically be 0 or 1)
     */
    List<EtlRun> findByStatusIn(List<EtlRun.EtlRunStatus> statuses);
    
    /**
     * Find ETL runs that failed
     */
    List<EtlRun> findByStatusOrderByStartTimeDesc(EtlRun.EtlRunStatus status);
    
    /**
     * Get ETL run statistics
     */
    @Query("SELECT COUNT(e) FROM EtlRun e WHERE e.status = :status")
    long countByStatus(@Param("status") EtlRun.EtlRunStatus status);
    
    /**
     * Find incomplete ETL runs (for recovery)
     */
    @Query("SELECT e FROM EtlRun e WHERE e.status IN ('STARTED', 'RUNNING') ORDER BY e.startTime DESC")
    List<EtlRun> findIncompleteRuns();
    
    /**
     * Get average processing time for completed runs
     */
    @Query("SELECT AVG(TIMESTAMPDIFF(SECOND, e.startTime, e.endTime)) FROM EtlRun e WHERE e.status = 'COMPLETED' AND e.endTime IS NOT NULL")
    Double getAverageProcessingTimeSeconds();
}
