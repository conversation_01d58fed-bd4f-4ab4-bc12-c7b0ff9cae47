package com.helios.etl.progress.repositories;

import com.helios.etl.progress.entities.TicketTransformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TicketTransformationRepository extends JpaRepository<TicketTransformation, Long> {
    
    /**
     * Find ticket transformation by ETL run and ticket ID
     */
    Optional<TicketTransformation> findByEtlRunIdAndTicketId(Long etlRunId, Integer ticketId);
    
    /**
     * Find all ticket transformations for an ETL run
     */
    List<TicketTransformation> findByEtlRunIdOrderByProcessingOrder(Long etlRunId);
    
    /**
     * Find ticket transformations by status for an ETL run
     */
    List<TicketTransformation> findByEtlRunIdAndStatusOrderByProcessingOrder(Long etlRunId, TicketTransformation.TransformationStatus status);
    
    /**
     * Find failed ticket transformations for an ETL run
     */
    List<TicketTransformation> findByEtlRunIdAndStatusInOrderByProcessingOrder(Long etlRunId, List<TicketTransformation.TransformationStatus> statuses);
    
    /**
     * Find the last successfully processed ticket for an ETL run
     */
    @Query("SELECT t FROM TicketTransformation t WHERE t.etlRun.id = :etlRunId AND t.status = 'SUCCESS' ORDER BY t.processingOrder DESC LIMIT 1")
    Optional<TicketTransformation> findLastSuccessfulTransformation(@Param("etlRunId") Long etlRunId);
    
    /**
     * Find tickets that need to be processed (for resume functionality)
     */
    @Query("SELECT t FROM TicketTransformation t WHERE t.etlRun.id = :etlRunId AND t.status IN ('PENDING', 'RETRY') ORDER BY t.processingOrder")
    List<TicketTransformation> findPendingTransformations(@Param("etlRunId") Long etlRunId);
    
    /**
     * Count transformations by status for an ETL run
     */
    long countByEtlRunIdAndStatus(Long etlRunId, TicketTransformation.TransformationStatus status);
    
    /**
     * Find tickets that have exceeded retry limit
     */
    @Query("SELECT t FROM TicketTransformation t WHERE t.etlRun.id = :etlRunId AND t.retryCount >= :maxRetries AND t.status = 'FAILED'")
    List<TicketTransformation> findExceededRetryLimit(@Param("etlRunId") Long etlRunId, @Param("maxRetries") Integer maxRetries);
    
    /**
     * Get processing statistics for an ETL run
     */
    @Query("SELECT t.status, COUNT(t) FROM TicketTransformation t WHERE t.etlRun.id = :etlRunId GROUP BY t.status")
    List<Object[]> getStatusStatistics(@Param("etlRunId") Long etlRunId);
    
    /**
     * Find transformations with errors
     */
    @Query("SELECT DISTINCT t FROM TicketTransformation t JOIN t.errors e WHERE t.etlRun.id = :etlRunId")
    List<TicketTransformation> findTransformationsWithErrors(@Param("etlRunId") Long etlRunId);
}
