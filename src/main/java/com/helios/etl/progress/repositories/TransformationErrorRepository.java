package com.helios.etl.progress.repositories;

import com.helios.etl.progress.entities.TransformationError;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TransformationErrorRepository extends JpaRepository<TransformationError, Long> {
    
    /**
     * Find errors for a specific ticket transformation
     */
    List<TransformationError> findByTicketTransformationIdOrderByOccurredAtDesc(Long ticketTransformationId);
    
    /**
     * Find errors by ETL run
     */
    @Query("SELECT e FROM TransformationError e WHERE e.ticketTransformation.etlRun.id = :etlRunId ORDER BY e.occurredAt DESC")
    List<TransformationError> findByEtlRunId(@Param("etlRunId") Long etlRunId);
    
    /**
     * Find errors by type
     */
    List<TransformationError> findByErrorTypeOrderByOccurredAtDesc(String errorType);
    
    /**
     * Find unresolved errors
     */
    List<TransformationError> findByResolutionStatusOrderByOccurredAtDesc(TransformationError.ResolutionStatus resolutionStatus);
    
    /**
     * Find errors within a date range
     */
    List<TransformationError> findByOccurredAtBetweenOrderByOccurredAtDesc(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Get error statistics by type
     */
    @Query("SELECT e.errorType, COUNT(e) FROM TransformationError e GROUP BY e.errorType ORDER BY COUNT(e) DESC")
    List<Object[]> getErrorStatisticsByType();
    
    /**
     * Get error statistics by resolution status
     */
    @Query("SELECT e.resolutionStatus, COUNT(e) FROM TransformationError e GROUP BY e.resolutionStatus")
    List<Object[]> getErrorStatisticsByResolutionStatus();
    
    /**
     * Find most common error messages
     */
    @Query("SELECT e.errorMessage, COUNT(e) FROM TransformationError e GROUP BY e.errorMessage ORDER BY COUNT(e) DESC")
    List<Object[]> getMostCommonErrors();
    
    /**
     * Count errors for an ETL run
     */
    @Query("SELECT COUNT(e) FROM TransformationError e WHERE e.ticketTransformation.etlRun.id = :etlRunId")
    long countByEtlRunId(@Param("etlRunId") Long etlRunId);
}
