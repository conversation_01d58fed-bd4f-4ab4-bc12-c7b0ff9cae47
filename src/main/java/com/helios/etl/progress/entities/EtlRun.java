package com.helios.etl.progress.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents an ETL execution session
 */
@Entity
@Table(name = "etl_runs")
@Getter
@Setter
public class EtlRun {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "run_id", unique = true, nullable = false)
    private String runId;
    
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private EtlRunStatus status;
    
    @Column(name = "total_tickets")
    private Integer totalTickets;
    
    @Column(name = "processed_tickets")
    private Integer processedTickets;
    
    @Column(name = "successful_tickets")
    private Integer successfulTickets;
    
    @Column(name = "failed_tickets")
    private Integer failedTickets;
    
    @Column(name = "last_processed_ticket_id")
    private Integer lastProcessedTicketId;
    
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
    
    @Column(name = "command_executed")
    private String commandExecuted;
    
    @OneToMany(mappedBy = "etlRun", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TicketTransformation> ticketTransformations = new ArrayList<>();
    
    public enum EtlRunStatus {
        STARTED,
        RUNNING,
        COMPLETED,
        FAILED,
        CANCELLED
    }
    
    // Convenience methods
    public void incrementProcessedTickets() {
        if (processedTickets == null) processedTickets = 0;
        processedTickets++;
    }
    
    public void incrementSuccessfulTickets() {
        if (successfulTickets == null) successfulTickets = 0;
        successfulTickets++;
    }
    
    public void incrementFailedTickets() {
        if (failedTickets == null) failedTickets = 0;
        failedTickets++;
    }
    
    public double getProgressPercentage() {
        if (totalTickets == null || totalTickets == 0) return 0.0;
        if (processedTickets == null) return 0.0;
        return (double) processedTickets / totalTickets * 100.0;
    }
}
