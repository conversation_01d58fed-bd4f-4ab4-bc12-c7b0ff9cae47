package com.helios.etl.progress.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Stores detailed error information for ticket transformations
 */
@Entity
@Table(name = "transformation_errors")
@Getter
@Setter
public class TransformationError {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ticket_transformation_id", nullable = false)
    private TicketTransformation ticketTransformation;
    
    @Column(name = "error_message", length = 2000, nullable = false)
    private String errorMessage;
    
    @Column(name = "error_type")
    private String errorType;
    
    @Column(name = "stack_trace", length = 5000)
    private String stackTrace;
    
    @Column(name = "occurred_at", nullable = false)
    private LocalDateTime occurredAt;
    
    @Column(name = "retry_attempt")
    private Integer retryAttempt;
    
    @Column(name = "resolution_status")
    @Enumerated(EnumType.STRING)
    private ResolutionStatus resolutionStatus = ResolutionStatus.UNRESOLVED;
    
    @Column(name = "resolution_note")
    private String resolutionNote;
    
    public enum ResolutionStatus {
        UNRESOLVED,
        RESOLVED,
        IGNORED,
        REQUIRES_MANUAL_INTERVENTION
    }
}
