package com.helios.etl.progress.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents the transformation status of an individual ticket
 */
@Entity
@Table(name = "ticket_transformations")
@Getter
@Setter
public class TicketTransformation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "etl_run_id", nullable = false)
    private EtlRun etlRun;
    
    @Column(name = "ticket_id", nullable = false)
    private Integer ticketId;
    
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TransformationStatus status;
    
    @Column(name = "processing_order")
    private Integer processingOrder;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    @Column(name = "created_issue_id")
    private Long createdIssueId;
    
    @Column(name = "transformation_note")
    private String transformationNote;
    
    @OneToMany(mappedBy = "ticketTransformation", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TransformationError> errors = new ArrayList<>();
    
    public enum TransformationStatus {
        PENDING,
        PROCESSING,
        SUCCESS,
        FAILED,
        SKIPPED,
        RETRY
    }
    
    // Convenience methods
    public void incrementRetryCount() {
        if (retryCount == null) retryCount = 0;
        retryCount++;
    }
    
    public long getProcessingDurationMillis() {
        if (startTime == null) return 0;
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
    
    public void addError(String errorMessage, String errorType, String stackTrace) {
        TransformationError error = new TransformationError();
        error.setTicketTransformation(this);
        error.setErrorMessage(errorMessage);
        error.setErrorType(errorType);
        error.setStackTrace(stackTrace);
        error.setOccurredAt(LocalDateTime.now());
        this.errors.add(error);
    }
}
