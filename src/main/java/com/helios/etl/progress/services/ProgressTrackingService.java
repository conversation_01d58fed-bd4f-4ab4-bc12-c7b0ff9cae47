package com.helios.etl.progress.services;

import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.entities.TicketTransformation;
import com.helios.etl.progress.entities.TransformationError;
import com.helios.etl.progress.repositories.EtlRunRepository;
import com.helios.etl.progress.repositories.TicketTransformationRepository;
import com.helios.etl.progress.repositories.TransformationErrorRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional(value = "progressTransactionManager", propagation = Propagation.REQUIRES_NEW)
public class ProgressTrackingService {
    
    private static final Logger log = LoggerFactory.getLogger(ProgressTrackingService.class);
    
    @Autowired
    private EtlRunRepository etlRunRepository;
    
    @Autowired
    private TicketTransformationRepository ticketTransformationRepository;
    
    @Autowired
    private TransformationErrorRepository transformationErrorRepository;
    
    /**
     * Start a new ETL run
     */
    public EtlRun startEtlRun(String command, int totalTickets) {
        log.info("Starting new ETL run with {} tickets", totalTickets);
        
        EtlRun etlRun = new EtlRun();
        etlRun.setRunId(UUID.randomUUID().toString());
        etlRun.setStartTime(LocalDateTime.now());
        etlRun.setStatus(EtlRun.EtlRunStatus.STARTED);
        etlRun.setTotalTickets(totalTickets);
        etlRun.setProcessedTickets(0);
        etlRun.setSuccessfulTickets(0);
        etlRun.setFailedTickets(0);
        etlRun.setCommandExecuted(command);
        
        return etlRunRepository.save(etlRun);
    }
    
    /**
     * Update ETL run status
     */
    public void updateEtlRunStatus(Long etlRunId, EtlRun.EtlRunStatus status) {
        Optional<EtlRun> etlRunOpt = etlRunRepository.findById(etlRunId);
        if (etlRunOpt.isPresent()) {
            EtlRun etlRun = etlRunOpt.get();
            etlRun.setStatus(status);
            if (status == EtlRun.EtlRunStatus.COMPLETED || status == EtlRun.EtlRunStatus.FAILED) {
                etlRun.setEndTime(LocalDateTime.now());
            }
            etlRunRepository.save(etlRun);
            log.info("Updated ETL run {} status to {}", etlRunId, status);
        }
    }
    
    /**
     * Complete ETL run
     */
    public void completeEtlRun(Long etlRunId, boolean success, String errorMessage) {
        Optional<EtlRun> etlRunOpt = etlRunRepository.findById(etlRunId);
        if (etlRunOpt.isPresent()) {
            EtlRun etlRun = etlRunOpt.get();
            etlRun.setStatus(success ? EtlRun.EtlRunStatus.COMPLETED : EtlRun.EtlRunStatus.FAILED);
            etlRun.setEndTime(LocalDateTime.now());
            if (errorMessage != null) {
                etlRun.setErrorMessage(errorMessage);
            }
            etlRunRepository.save(etlRun);
            log.info("Completed ETL run {} with status: {}", etlRunId, etlRun.getStatus());
        }
    }
    
    /**
     * Start ticket transformation
     */
    public TicketTransformation startTicketTransformation(Long etlRunId, Integer ticketId, int processingOrder) {
        log.debug("Starting ticket transformation for ticket {} in ETL run {}", ticketId, etlRunId);

        Optional<EtlRun> etlRunOpt = etlRunRepository.findById(etlRunId);
        if (etlRunOpt.isEmpty()) {
            throw new IllegalArgumentException("ETL run not found: " + etlRunId);
        }

        TicketTransformation transformation = new TicketTransformation();
        transformation.setEtlRun(etlRunOpt.get());
        transformation.setTicketId(ticketId);
        transformation.setStartTime(LocalDateTime.now());
        transformation.setStatus(TicketTransformation.TransformationStatus.PROCESSING);
        transformation.setProcessingOrder(processingOrder);

        TicketTransformation saved = ticketTransformationRepository.save(transformation);
        log.debug("Successfully saved ticket transformation with ID {} for ticket {}", saved.getId(), ticketId);
        return saved;
    }
    
    /**
     * Complete ticket transformation successfully
     */
    public void completeTicketTransformation(Long transformationId, Long createdIssueId, String note) {
        Optional<TicketTransformation> transformationOpt = ticketTransformationRepository.findById(transformationId);
        if (transformationOpt.isPresent()) {
            TicketTransformation transformation = transformationOpt.get();
            transformation.setEndTime(LocalDateTime.now());
            transformation.setStatus(TicketTransformation.TransformationStatus.SUCCESS);
            transformation.setCreatedIssueId(createdIssueId);
            transformation.setTransformationNote(note);
            
            ticketTransformationRepository.save(transformation);
            
            // Update ETL run statistics
            EtlRun etlRun = transformation.getEtlRun();
            etlRun.incrementProcessedTickets();
            etlRun.incrementSuccessfulTickets();
            etlRun.setLastProcessedTicketId(transformation.getTicketId());
            etlRunRepository.save(etlRun);
            
            log.debug("Completed ticket transformation for ticket {}", transformation.getTicketId());
        }
    }
    
    /**
     * Fail ticket transformation
     */
    public void failTicketTransformation(Long transformationId, String errorMessage, String errorType, String stackTrace) {
        Optional<TicketTransformation> transformationOpt = ticketTransformationRepository.findById(transformationId);
        if (transformationOpt.isPresent()) {
            TicketTransformation transformation = transformationOpt.get();
            transformation.setEndTime(LocalDateTime.now());
            transformation.setStatus(TicketTransformation.TransformationStatus.FAILED);
            transformation.incrementRetryCount();
            
            // Add error details
            transformation.addError(errorMessage, errorType, stackTrace);
            
            ticketTransformationRepository.save(transformation);
            
            // Update ETL run statistics
            EtlRun etlRun = transformation.getEtlRun();
            etlRun.incrementProcessedTickets();
            etlRun.incrementFailedTickets();
            etlRun.setLastProcessedTicketId(transformation.getTicketId());
            etlRunRepository.save(etlRun);
            
            log.warn("Failed ticket transformation for ticket {}: {}", transformation.getTicketId(), errorMessage);
        }
    }
    
    /**
     * Get the most recent ETL run
     */
    public Optional<EtlRun> getLastEtlRun() {
        return etlRunRepository.findTopByOrderByStartTimeDesc();
    }
    
    /**
     * Get incomplete ETL runs (for recovery)
     */
    public List<EtlRun> getIncompleteEtlRuns() {
        return etlRunRepository.findIncompleteRuns();
    }
    
    /**
     * Get tickets that need to be processed for resume functionality
     */
    public List<TicketTransformation> getPendingTransformations(Long etlRunId) {
        return ticketTransformationRepository.findPendingTransformations(etlRunId);
    }
    
    /**
     * Get ETL run progress information
     */
    public EtlRunProgress getEtlRunProgress(Long etlRunId) {
        Optional<EtlRun> etlRunOpt = etlRunRepository.findById(etlRunId);
        if (etlRunOpt.isEmpty()) {
            return null;
        }
        
        EtlRun etlRun = etlRunOpt.get();
        long totalErrors = transformationErrorRepository.countByEtlRunId(etlRunId);
        
        return new EtlRunProgress(
            etlRun.getId(),
            etlRun.getRunId(),
            etlRun.getStatus(),
            etlRun.getTotalTickets(),
            etlRun.getProcessedTickets(),
            etlRun.getSuccessfulTickets(),
            etlRun.getFailedTickets(),
            totalErrors,
            etlRun.getProgressPercentage(),
            etlRun.getStartTime(),
            etlRun.getEndTime()
        );
    }
    
    /**
     * Check if there's already a running ETL process
     */
    public boolean isEtlRunning() {
        List<EtlRun> runningRuns = etlRunRepository.findByStatusIn(
            List.of(EtlRun.EtlRunStatus.STARTED, EtlRun.EtlRunStatus.RUNNING)
        );
        return !runningRuns.isEmpty();
    }

    /**
     * Force-fail all running/started ETL runs (to clear stale locks)
     * Returns the number of runs affected.
     */
    public int failAllRunningRuns(String reason) {
        List<EtlRun> runningRuns = etlRunRepository.findByStatusIn(
            List.of(EtlRun.EtlRunStatus.STARTED, EtlRun.EtlRunStatus.RUNNING)
        );
        int count = 0;
        for (EtlRun run : runningRuns) {
            run.setStatus(EtlRun.EtlRunStatus.FAILED);
            run.setEndTime(LocalDateTime.now());
            String msg = (reason == null || reason.isBlank()) ? "Force-failed by CLI" : reason;
            run.setErrorMessage(msg);
            etlRunRepository.save(run);
            count++;
            log.warn("Force-failed stale ETL run {} (runId={})", run.getId(), run.getRunId());
        }
        if (count > 0) {
            log.info("Cleared {} stale running ETL run(s)", count);
        }
        return count;
    }

    /**
     * Get ETL run by ID
     */
    public Optional<EtlRun> getEtlRun(Long etlRunId) {
        return etlRunRepository.findById(etlRunId);
    }

    /**
     * Get all transformations for an ETL run
     */
    public List<TicketTransformation> getAllTransformations(Long etlRunId) {
        return ticketTransformationRepository.findByEtlRunIdOrderByProcessingOrder(etlRunId);
    }
    
    /**
     * Progress information DTO
     */
    public static class EtlRunProgress {
        public final Long etlRunId;
        public final String runId;
        public final EtlRun.EtlRunStatus status;
        public final Integer totalTickets;
        public final Integer processedTickets;
        public final Integer successfulTickets;
        public final Integer failedTickets;
        public final Long totalErrors;
        public final Double progressPercentage;
        public final LocalDateTime startTime;
        public final LocalDateTime endTime;
        
        public EtlRunProgress(Long etlRunId, String runId, EtlRun.EtlRunStatus status,
                             Integer totalTickets, Integer processedTickets, Integer successfulTickets,
                             Integer failedTickets, Long totalErrors, Double progressPercentage,
                             LocalDateTime startTime, LocalDateTime endTime) {
            this.etlRunId = etlRunId;
            this.runId = runId;
            this.status = status;
            this.totalTickets = totalTickets;
            this.processedTickets = processedTickets;
            this.successfulTickets = successfulTickets;
            this.failedTickets = failedTickets;
            this.totalErrors = totalErrors;
            this.progressPercentage = progressPercentage;
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }
}
