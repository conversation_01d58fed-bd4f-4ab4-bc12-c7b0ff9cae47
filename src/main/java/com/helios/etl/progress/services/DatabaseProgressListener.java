package com.helios.etl.progress.services;

import com.helios.etl.progress.entities.TicketTransformation;
import com.helios.etl.services.ProgressListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Enhanced ProgressListener that writes progress to SQLite database
 * while also delegating to another ProgressListener (like the CLI progress bar)
 */
public class DatabaseProgressListener implements ProgressListener {
    
    private static final Logger log = LoggerFactory.getLogger(DatabaseProgressListener.class);
    
    private final ProgressTrackingService progressTrackingService;
    private final Long etlRunId;
    private final ProgressListener delegateListener;
    
    private TicketTransformation currentTransformation;
    private int currentTicketId;
    
    public DatabaseProgressListener(ProgressTrackingService progressTrackingService, 
                                  Long etlRunId, 
                                  ProgressListener delegateListener) {
        this.progressTrackingService = progressTrackingService;
        this.etlRunId = etlRunId;
        this.delegateListener = delegateListener;
    }
    
    /**
     * Called when starting to process a new ticket
     */
    public void startTicketProcessing(int ticketId, int processingOrder) {
        try {
            log.debug("Attempting to start ticket processing for ticket {} (order: {})", ticketId, processingOrder);
            this.currentTicketId = ticketId;
            this.currentTransformation = progressTrackingService.startTicketTransformation(
                etlRunId, ticketId, processingOrder);
            log.debug("Successfully started processing ticket {} (order: {})", ticketId, processingOrder);
        } catch (Exception e) {
            log.error("Failed to start ticket transformation tracking for ticket {}: {}",
                     ticketId, e.getMessage(), e);
        }
    }
    
    @Override
    public void onProgress(int total, int current, String note) {
        try {
            // Handle the transformation result
            if (currentTransformation != null) {
                if ("OK".equals(note)) {
                    // Success case
                    progressTrackingService.completeTicketTransformation(
                        currentTransformation.getId(), null, "Transformation completed successfully");
                } else if ("ERR".equals(note)) {
                    // Error case
                    progressTrackingService.failTicketTransformation(
                        currentTransformation.getId(), 
                        "Transformation failed", 
                        "TRANSFORMATION_ERROR", 
                        null);
                }
                currentTransformation = null;
            }
            
            // Delegate to the original progress listener (e.g., CLI progress bar)
            if (delegateListener != null) {
                delegateListener.onProgress(total, current, note);
            }
            
        } catch (Exception e) {
            log.error("Failed to update progress tracking: {}", e.getMessage());
            // Still delegate to ensure UI progress continues
            if (delegateListener != null) {
                delegateListener.onProgress(total, current, note);
            }
        }
    }
    
    /**
     * Called when a ticket transformation fails with detailed error information
     */
    public void onTicketTransformationError(int ticketId, String errorMessage, Exception exception) {
        try {
            if (currentTransformation != null && currentTransformation.getTicketId().equals(ticketId)) {
                String stackTrace = exception != null ? getStackTrace(exception) : null;
                String errorType = exception != null ? exception.getClass().getSimpleName() : "UNKNOWN_ERROR";
                
                progressTrackingService.failTicketTransformation(
                    currentTransformation.getId(), 
                    errorMessage, 
                    errorType, 
                    stackTrace);
                
                currentTransformation = null;
            }
        } catch (Exception e) {
            log.error("Failed to record ticket transformation error: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Called when a ticket transformation succeeds with additional details
     */
    public void onTicketTransformationSuccess(int ticketId, Long createdIssueId, String note) {
        try {
            if (currentTransformation != null && currentTransformation.getTicketId().equals(ticketId)) {
                progressTrackingService.completeTicketTransformation(
                    currentTransformation.getId(), 
                    createdIssueId, 
                    note);
                
                currentTransformation = null;
            }
        } catch (Exception e) {
            log.error("Failed to record ticket transformation success: {}", e.getMessage());
        }
    }
    
    private String getStackTrace(Exception exception) {
        if (exception == null) return null;
        
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        String stackTrace = sw.toString();
        
        // Limit stack trace length to avoid database issues
        if (stackTrace.length() > 4000) {
            stackTrace = stackTrace.substring(0, 4000) + "... (truncated)";
        }
        
        return stackTrace;
    }
}
