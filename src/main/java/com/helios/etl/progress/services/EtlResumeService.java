package com.helios.etl.progress.services;

import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.entities.TicketTransformation;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Tickets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service for resuming ETL operations from the last successful point
 */
@Service
public class EtlResumeService {
    
    private static final Logger log = LoggerFactory.getLogger(EtlResumeService.class);
    
    @Autowired
    private ProgressTrackingService progressTrackingService;
    
    @Autowired
    private CacheMemory cacheMemory;
    
    /**
     * Check if there are incomplete ETL runs that can be resumed
     */
    public boolean hasIncompleteRuns() {
        List<EtlRun> incompleteRuns = progressTrackingService.getIncompleteEtlRuns();
        return !incompleteRuns.isEmpty();
    }
    
    /**
     * Get the most recent incomplete ETL run
     */
    public Optional<EtlRun> getLastIncompleteRun() {
        List<EtlRun> incompleteRuns = progressTrackingService.getIncompleteEtlRuns();
        return incompleteRuns.isEmpty() ? Optional.empty() : Optional.of(incompleteRuns.get(0));
    }
    
    /**
     * Get tickets that still need to be processed for resume functionality
     */
    public List<Tickets> getTicketsToResume(Long etlRunId) {
        // Get all tickets from cache
        Set<Tickets> allTickets = cacheMemory.getTickets();
        if (allTickets == null || allTickets.isEmpty()) {
            log.warn("No tickets found in cache for resume operation");
            return List.of();
        }
        
        // Get pending transformations from database
        List<TicketTransformation> pendingTransformations = progressTrackingService.getPendingTransformations(etlRunId);
        Set<Integer> pendingTicketIds = pendingTransformations.stream()
            .map(TicketTransformation::getTicketId)
            .collect(Collectors.toSet());
        
        // If no pending transformations found, get all tickets that haven't been processed yet
        if (pendingTicketIds.isEmpty()) {
            // Get all processed ticket IDs
            List<TicketTransformation> allTransformations = progressTrackingService.getAllTransformations(etlRunId);
            Set<Integer> processedTicketIds = allTransformations.stream()
                .map(TicketTransformation::getTicketId)
                .collect(Collectors.toSet());
            
            // Return tickets that haven't been processed
            return allTickets.stream()
                .filter(ticket -> !processedTicketIds.contains(ticket.getIdTickets()))
                .collect(Collectors.toList());
        }
        
        // Return tickets that are marked as pending
        return allTickets.stream()
            .filter(ticket -> pendingTicketIds.contains(ticket.getIdTickets()))
            .collect(Collectors.toList());
    }
    
    /**
     * Get resume information for display
     */
    public ResumeInfo getResumeInfo(Long etlRunId) {
        Optional<EtlRun> etlRunOpt = progressTrackingService.getEtlRun(etlRunId);
        if (etlRunOpt.isEmpty()) {
            return null;
        }
        
        EtlRun etlRun = etlRunOpt.get();
        List<Tickets> ticketsToResume = getTicketsToResume(etlRunId);
        
        return new ResumeInfo(
            etlRun.getId(),
            etlRun.getRunId(),
            etlRun.getStartTime(),
            etlRun.getTotalTickets(),
            etlRun.getProcessedTickets(),
            etlRun.getSuccessfulTickets(),
            etlRun.getFailedTickets(),
            ticketsToResume.size(),
            etlRun.getLastProcessedTicketId()
        );
    }
    
    /**
     * Mark an incomplete ETL run as cancelled
     */
    public void cancelIncompleteRun(Long etlRunId) {
        progressTrackingService.updateEtlRunStatus(etlRunId, EtlRun.EtlRunStatus.CANCELLED);
        log.info("Marked ETL run {} as cancelled", etlRunId);
    }
    
    /**
     * Resume information DTO
     */
    public static class ResumeInfo {
        public final Long etlRunId;
        public final String runId;
        public final java.time.LocalDateTime startTime;
        public final Integer totalTickets;
        public final Integer processedTickets;
        public final Integer successfulTickets;
        public final Integer failedTickets;
        public final Integer remainingTickets;
        public final Integer lastProcessedTicketId;
        
        public ResumeInfo(Long etlRunId, String runId, java.time.LocalDateTime startTime,
                         Integer totalTickets, Integer processedTickets, Integer successfulTickets,
                         Integer failedTickets, Integer remainingTickets, Integer lastProcessedTicketId) {
            this.etlRunId = etlRunId;
            this.runId = runId;
            this.startTime = startTime;
            this.totalTickets = totalTickets;
            this.processedTickets = processedTickets;
            this.successfulTickets = successfulTickets;
            this.failedTickets = failedTickets;
            this.remainingTickets = remainingTickets;
            this.lastProcessedTicketId = lastProcessedTicketId;
        }
        
        public double getProgressPercentage() {
            if (totalTickets == null || totalTickets == 0) return 0.0;
            if (processedTickets == null) return 0.0;
            return (double) processedTickets / totalTickets * 100.0;
        }
    }
}
