# Test configuration for ETL application
spring.application.name=etl-test
spring.main.web-application-type=none

# Use in-memory H2 for main datasource in tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate Configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# SQLite Progress Tracking Database for tests (in-memory)
progress.datasource.url=jdbc:sqlite::memory:
progress.datasource.driver-class-name=org.sqlite.JDBC

# Disable auto-configuration that might interfere with tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration,org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration

# Test-specific SQL Server configuration (not used in tests)
sqlserver.url=jdbc:h2:mem:sourcedb
sqlserver.username=sa
sqlserver.password=
sqlserver.driver-class-name=org.h2.Driver

# Logging configuration for tests
logging.level.com.helios.etl=DEBUG
logging.level.org.springframework.data=WARN
logging.level.org.hibernate=WARN
