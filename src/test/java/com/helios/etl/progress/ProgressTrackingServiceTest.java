package com.helios.etl.progress;

import com.helios.etl.cli.EtlCommandLineRunner;
import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.entities.TicketTransformation;
import com.helios.etl.progress.services.ProgressTrackingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify progress tracking functionality and database connectivity
 */
@SpringBootTest(classes = EtlCommandLineRunner.class)
@ActiveProfiles("test")
public class ProgressTrackingServiceTest {

    @Autowired
    private ProgressTrackingService progressTrackingService;

    @Test
    @Transactional("progressTransactionManager")
    public void testStartEtlRun() {
        // Test basic ETL run creation
        EtlRun etlRun = progressTrackingService.startEtlRun("test-command", 10);
        
        assertNotNull(etlRun);
        assertNotNull(etlRun.getId());
        assertNotNull(etlRun.getRunId());
        assertEquals("test-command", etlRun.getCommandExecuted());
        assertEquals(10, etlRun.getTotalTickets());
        assertEquals(EtlRun.EtlRunStatus.STARTED, etlRun.getStatus());
    }

    @Test
    @Transactional("progressTransactionManager")
    public void testStartTicketTransformation() {
        // First create an ETL run
        EtlRun etlRun = progressTrackingService.startEtlRun("test-command", 5);
        assertNotNull(etlRun.getId());
        
        // Then start a ticket transformation
        TicketTransformation transformation = progressTrackingService.startTicketTransformation(
            etlRun.getId(), 12345, 1);
        
        assertNotNull(transformation);
        assertNotNull(transformation.getId());
        assertEquals(12345, transformation.getTicketId());
        assertEquals(1, transformation.getProcessingOrder());
        assertEquals(TicketTransformation.TransformationStatus.PROCESSING, transformation.getStatus());
        assertEquals(etlRun.getId(), transformation.getEtlRun().getId());
    }

    @Test
    @Transactional("progressTransactionManager")
    public void testCompleteTicketTransformation() {
        // Create ETL run and ticket transformation
        EtlRun etlRun = progressTrackingService.startEtlRun("test-command", 3);
        TicketTransformation transformation = progressTrackingService.startTicketTransformation(
            etlRun.getId(), 67890, 1);
        
        // Complete the transformation
        progressTrackingService.completeTicketTransformation(
            transformation.getId(), 999L, "Test completion");
        
        // Verify completion (would need to fetch from repository to verify)
        // This test mainly ensures no exceptions are thrown
        assertNotNull(transformation.getId());
    }

    @Test
    @Transactional("progressTransactionManager")
    public void testFailTicketTransformation() {
        // Create ETL run and ticket transformation
        EtlRun etlRun = progressTrackingService.startEtlRun("test-command", 2);
        TicketTransformation transformation = progressTrackingService.startTicketTransformation(
            etlRun.getId(), 11111, 1);
        
        // Fail the transformation
        progressTrackingService.failTicketTransformation(
            transformation.getId(), "Test error message", "TEST_ERROR", "Stack trace here");
        
        // Verify failure (would need to fetch from repository to verify)
        // This test mainly ensures no exceptions are thrown
        assertNotNull(transformation.getId());
    }
}
