@echo off
REM ETL Build Script for Windows
REM This script handles building the project when Maven wrapper has issues

setlocal enabledelayedexpansion

echo ETL Project Build Script
echo ========================

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 17 or higher
    exit /b 1
)

echo Java: OK

REM Try to use Maven wrapper first
if exist mvnw.cmd (
    echo Trying Maven wrapper...
    call mvnw.cmd --version >nul 2>&1
    if !errorlevel! equ 0 (
        echo Using Maven wrapper
        set MAVEN_CMD=mvnw.cmd
        goto :build
    )
)

REM Check if Maven is installed directly
mvn --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Using system Maven
    set MAVEN_CMD=mvn
    goto :build
) else (
    echo Warning: Neither Maven wrapper nor system Maven found
    echo Attempting to download and fix Maven wrapper...
    goto :fix_maven
)

:fix_maven
echo Downloading Maven wrapper...
REM Create .mvn directory if it doesn't exist
if not exist .mvn mkdir .mvn
if not exist .mvn\wrapper mkdir .mvn\wrapper

REM Download wrapper files using PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://repo.maven.apache.org/maven2/org/apache/maven/wrapper/maven-wrapper/3.2.0/maven-wrapper-3.2.0.jar' -OutFile '.mvn\wrapper\maven-wrapper.jar'}"
if %errorlevel% neq 0 (
    echo Failed to download Maven wrapper JAR
    goto :manual_build
)

REM Create wrapper properties
echo distributionUrl=https://repo.maven.apache.org/maven2/org/apache/maven/apache-maven/3.9.5/apache-maven-3.9.5-bin.zip > .mvn\wrapper\maven-wrapper.properties

REM Create mvnw.cmd if it doesn't exist
if not exist mvnw.cmd (
    echo Creating mvnw.cmd...
    echo @echo off > mvnw.cmd
    echo setlocal >> mvnw.cmd
    echo set MAVEN_PROJECTBASEDIR=%%~dp0 >> mvnw.cmd
    echo java -classpath ".mvn\wrapper\maven-wrapper.jar" "-Dmaven.multiModuleProjectDirectory=%%MAVEN_PROJECTBASEDIR%%" org.apache.maven.wrapper.MavenWrapperMain %%* >> mvnw.cmd
)

set MAVEN_CMD=mvnw.cmd
echo Maven wrapper fixed!

:build
echo Building project with %MAVEN_CMD%...
call %MAVEN_CMD% clean package -DskipTests

if %errorlevel% neq 0 (
    echo Build failed!
    goto :manual_build
)

echo Build successful!
echo JAR file should be at: target\etl-0.0.1-SNAPSHOT.jar

if exist target\etl-0.0.1-SNAPSHOT.jar (
    echo ✓ JAR file created successfully
    echo You can now run: etl-cli.bat --run
) else (
    echo ✗ JAR file not found, build may have failed
)

goto :end

:manual_build
echo ================================================
echo Manual Build Instructions:
echo ================================================
echo 1. Install Maven from: https://maven.apache.org/download.cgi
echo 2. Add Maven to your PATH
echo 3. Run: mvn clean package -DskipTests
echo ================================================

:end
endlocal
