#!/bin/bash
# ETL Build Script for Unix/Linux/macOS
# This script handles building the project when Maven wrapper has issues

set -e

echo "ETL Project Build Script"
echo "========================"

# Auto-detect JAVA_HOME if not set
if [ -z "$JAVA_HOME" ]; then
    echo "JAVA_HOME not set, attempting to detect Java installation..."

    # Try common Java locations
    for java_dir in \
        /usr/lib/jvm/java-* \
        /usr/lib/jvm/jdk-* \
        /usr/lib/jvm/openjdk-* \
        /opt/java/* \
        /Library/Java/JavaVirtualMachines/*/Contents/Home \
        /System/Library/Java/JavaVirtualMachines/*/Contents/Home; do

        if [ -d "$java_dir" ] && [ -x "$java_dir/bin/java" ]; then
            export JAVA_HOME="$java_dir"
            echo "Found Java at: $JAVA_HOME"
            break
        fi
    done
fi

# Check if Java is available
if [ -n "$JAVA_HOME" ]; then
    if ! "$JAVA_HOME/bin/java" -version >/dev/null 2>&1; then
        echo "Error: Java installation test failed"
        exit 1
    fi
elif ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 17 or higher"
    exit 1
fi

echo "Java: OK"

# Try to use Maven wrapper first
MAVEN_CMD=""
if [ -f "./mvnw" ] && [ -x "./mvnw" ]; then
    echo "Trying Maven wrapper..."
    if ./mvnw --version >/dev/null 2>&1; then
        echo "Using Maven wrapper"
        MAVEN_CMD="./mvnw"
    else
        echo "Maven wrapper exists but not working, attempting to fix..."
        chmod +x ./mvnw
        if ./mvnw --version >/dev/null 2>&1; then
            MAVEN_CMD="./mvnw"
        fi
    fi
fi

# Check if Maven is installed directly
if [ -z "$MAVEN_CMD" ]; then
    if command -v mvn &> /dev/null; then
        echo "Using system Maven"
        MAVEN_CMD="mvn"
    else
        echo "Warning: Neither Maven wrapper nor system Maven found"
        echo "Attempting to download and fix Maven wrapper..."

        # Create .mvn directory if it doesn't exist
        mkdir -p .mvn/wrapper

        # Download wrapper files
        echo "Downloading Maven wrapper..."
        curl -L -o .mvn/wrapper/maven-wrapper.jar \
            "https://repo.maven.apache.org/maven2/org/apache/maven/wrapper/maven-wrapper/3.2.0/maven-wrapper.jar"

        if [ $? -ne 0 ]; then
            echo "Failed to download Maven wrapper JAR"
            echo ""
            echo "Manual Build Instructions:"
            echo "=========================="
            echo "1. Install Maven from: https://maven.apache.org/download.cgi"
            echo "2. Add Maven to your PATH"
            echo "3. Run: mvn clean package -DskipTests"
            echo "=========================="
            exit 1
        fi

        # Create wrapper properties
        echo "distributionUrl=https://repo.maven.apache.org/maven2/org/apache/maven/apache-maven/3.9.5/apache-maven-3.9.5-bin.zip" > .mvn/wrapper/maven-wrapper.properties

        # Create mvnw if it doesn't exist
        if [ ! -f "./mvnw" ]; then
            echo "Creating mvnw..."
            cat > mvnw << 'EOF'
#!/bin/bash
# Maven Wrapper Script

MAVEN_PROJECTBASEDIR="$(cd "$(dirname "$0")" && pwd)"
export MAVEN_PROJECTBASEDIR

exec java -classpath "$MAVEN_PROJECTBASEDIR/.mvn/wrapper/maven-wrapper.jar" \
    "-Dmaven.multiModuleProjectDirectory=$MAVEN_PROJECTBASEDIR" \
    org.apache.maven.wrapper.MavenWrapperMain "$@"
EOF
            chmod +x mvnw
        fi

        MAVEN_CMD="./mvnw"
        echo "Maven wrapper fixed!"
    fi
fi

echo "Building project with $MAVEN_CMD..."
$MAVEN_CMD clean package -DskipTests

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"
echo "JAR file should be at: target/etl-0.0.1-SNAPSHOT.jar"

if [ -f "target/etl-0.0.1-SNAPSHOT.jar" ]; then
    echo "✓ JAR file created successfully"
    echo "You can now run: ./etl-cli.sh --run"
else
    echo "✗ JAR file not found, build may have failed"
fi
